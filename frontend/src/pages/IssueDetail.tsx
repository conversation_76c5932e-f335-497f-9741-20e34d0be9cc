import React, { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Button,
  Chip,
  Divider,
  Grid,
  Paper,
  Typography,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemIcon,
  TextField,
  IconButton,
  Tab,
  Tabs,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Breadcrumbs,
  Link,
  Snackbar,
  Alert,
  Tooltip,
  CircularProgress
} from '@mui/material';
// Timeline components are not available, we'll use regular components instead
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Send as SendIcon,
  Attachment as AttachmentIcon,
  AttachFile as AttachFileIcon,
  Person as PersonIcon,
  Add as AddIcon,
  ArrowBack as ArrowBackIcon,
  Home as HomeIcon,
  BugReport as BugReportIcon,
  Close as CloseIcon,
  Description as DescriptionIcon,
  Visibility as VisibilityIcon,
  CalendarToday as CalendarTodayIcon,
  Link as LinkIcon,
  Label as LabelIcon,
  CheckBox as CheckBoxIcon,
  Comment as CommentIcon,
  History as HistoryIcon,
  SubdirectoryArrowRight as SubdirectoryArrowRightIcon,
  Forum as ForumIcon,
  ChatBubbleOutline as ChatBubbleOutlineIcon,
  NavigateNext as NavigateNextIcon,
  CloudUpload as CloudUploadIcon,
  CheckCircle as CheckCircleIcon,
  Folder as FolderIcon,
  FolderOpen as FolderOpenIcon,
  InsertDriveFile as InsertDriveFileIcon,
  GetApp as GetAppIcon,
  AddCircle as AddCircleIcon,
  ArrowRightAlt as ArrowRightAltIcon
} from '@mui/icons-material';
import { blue, green } from '@mui/material/colors';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { ROUTES } from '../config/routes';
import {
  getPriorityColor,
  getSeverityColor,
  getStatusColor,
  getPriorityChipColor,
  getSeverityChipColor,
  getContrastTextColor
} from '../utils/colorUtils';
import {
  fetchIssueById,
  deleteIssue,
  addComment,
  deleteComment,
  addAttachment,
  deleteAttachment,
  addWatcher,
  removeWatcher
} from '../store/slices/issueSlice';
import IssueForm from '../components/IssueForm';
import LabelManager from '../components/LabelManager';
import ChecklistManager from '../components/ChecklistManager';
import IssueTypeSpecificFields from '../components/IssueTypeSpecificFields';
// import { formatDate, formatDateTime } from '../utils/dateUtils';

// Simple date formatting functions
const formatDate = (dateValue: any): string => {
  if (!dateValue) return 'Not set';

  try {
    let date: Date;

    // Handle array format from backend [year, month, day, hour, minute, second, nanosecond]
    if (Array.isArray(dateValue)) {
      if (dateValue.length >= 3) {
        // Month is 0-indexed in JavaScript Date constructor
        date = new Date(dateValue[0], dateValue[1] - 1, dateValue[2]);
      } else {
        return 'Invalid date';
      }
    } else {
      date = new Date(dateValue);
    }

    if (isNaN(date.getTime())) return 'Invalid date';
    return date.toLocaleDateString();
  } catch {
    return 'Invalid date';
  }
};

const formatDateTime = (dateValue: any): string => {
  if (!dateValue) return 'Not set';

  try {
    let date: Date;

    // Handle array format from backend [year, month, day, hour, minute, second, nanosecond]
    if (Array.isArray(dateValue)) {
      if (dateValue.length >= 6) {
        // Month is 0-indexed in JavaScript Date constructor
        date = new Date(dateValue[0], dateValue[1] - 1, dateValue[2], dateValue[3], dateValue[4], dateValue[5]);
      } else if (dateValue.length >= 3) {
        date = new Date(dateValue[0], dateValue[1] - 1, dateValue[2]);
      } else {
        return 'Invalid date';
      }
    } else {
      date = new Date(dateValue);
    }

    if (isNaN(date.getTime())) return 'Invalid date';
    return date.toLocaleString();
  } catch {
    return 'Invalid date';
  }
};

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`issue-tabpanel-${index}`}
      aria-labelledby={`issue-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const IssueDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { currentIssue, loading } = useAppSelector((state) => state.issues);
  const { user } = useAppSelector((state) => state.auth);

  const [tabValue, setTabValue] = useState(0);
  const [commentText, setCommentText] = useState('');

  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);
  const [isUploadingAttachment, setIsUploadingAttachment] = useState(false);

  useEffect(() => {
    if (id) {
      dispatch(fetchIssueById(parseInt(id)));
    }
  }, [dispatch, id]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleEditIssue = () => {
    navigate(ROUTES.APP.ISSUES.EDIT(id!));
  };

  const handleOpenDeleteDialog = () => {
    setOpenDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
  };

  const handleDeleteIssue = () => {
    if (id) {
      dispatch(deleteIssue(parseInt(id)))
        .then(() => {
          navigate(ROUTES.APP.ISSUES.ROOT);
        });
    }
  };

  const handleCommentSubmit = () => {
    if (!id) {
      setSnackbarMessage('Issue ID is missing');
      setSnackbarOpen(true);
      return;
    }

    if (!commentText.trim()) {
      setSnackbarMessage('Please enter a comment');
      setSnackbarOpen(true);
      return;
    }

    setIsSubmittingComment(true);
    dispatch(addComment({
      issueId: parseInt(id),
      content: commentText.trim()
    })).unwrap()
      .then(() => {
        setCommentText('');
        dispatch(fetchIssueById(parseInt(id)));
        setSnackbarMessage('Comment added successfully');
        setSnackbarOpen(true);
      })
      .catch((error) => {
        console.error('Error adding comment:', error);
        setSnackbarMessage(error || 'Failed to add comment');
        setSnackbarOpen(true);
      })
      .finally(() => {
        setIsSubmittingComment(false);
      });
  };

  const handleDeleteComment = (commentId: number) => {
    dispatch(deleteComment(commentId)).unwrap()
      .then(() => {
        if (id) {
          dispatch(fetchIssueById(parseInt(id)));
          setSnackbarMessage('Comment deleted successfully');
          setSnackbarOpen(true);
        }
      })
      .catch((error) => {
        console.error('Error deleting comment:', error);
        setSnackbarMessage('Failed to delete comment');
        setSnackbarOpen(true);
      });
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      setFile(event.target.files[0]);
    }
  };

  const handleUploadAttachment = () => {
    if (!id) {
      setSnackbarMessage('Issue ID is missing');
      setSnackbarOpen(true);
      return;
    }

    if (!file) {
      setSnackbarMessage('Please select a file to upload');
      setSnackbarOpen(true);
      return;
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      setSnackbarMessage('File size must be less than 10MB');
      setSnackbarOpen(true);
      return;
    }

    setIsUploadingAttachment(true);
    const formData = new FormData();
    formData.append('file', file);

    dispatch(addAttachment({
      issueId: parseInt(id),
      file: formData
    })).unwrap()
      .then(() => {
        setFile(null);
        // Reset the file input
        const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
        if (fileInput) {
          fileInput.value = '';
        }
        dispatch(fetchIssueById(parseInt(id)));
        setSnackbarMessage('Attachment uploaded successfully');
        setSnackbarOpen(true);
      })
      .catch((error) => {
        console.error('Error uploading attachment:', error);
        setSnackbarMessage(error || 'Failed to upload attachment');
        setSnackbarOpen(true);
      })
      .finally(() => {
        setIsUploadingAttachment(false);
      });
  };

  const handleDeleteAttachment = (attachmentId: number) => {
    dispatch(deleteAttachment(attachmentId)).unwrap()
      .then(() => {
        if (id) {
          dispatch(fetchIssueById(parseInt(id)));
          setSnackbarMessage('Attachment deleted successfully');
          setSnackbarOpen(true);
        }
      })
      .catch((error) => {
        console.error('Error deleting attachment:', error);
        setSnackbarMessage('Failed to delete attachment');
        setSnackbarOpen(true);
      });
  };

  const handleAddWatcher = () => {
    if (id && user) {
      dispatch(addWatcher({
        issueId: parseInt(id),
        userId: user.id
      })).unwrap()
        .then(() => {
          dispatch(fetchIssueById(parseInt(id)));
          setSnackbarMessage('You are now watching this issue');
          setSnackbarOpen(true);
        })
        .catch((error) => {
          console.error('Error adding watcher:', error);
          setSnackbarMessage('Failed to add watcher');
          setSnackbarOpen(true);
        });
    }
  };

  const handleRemoveWatcher = (userId: number) => {
    if (id) {
      dispatch(removeWatcher({
        issueId: parseInt(id),
        userId
      })).unwrap()
        .then(() => {
          dispatch(fetchIssueById(parseInt(id)));
          setSnackbarMessage('Watcher removed successfully');
          setSnackbarOpen(true);
        })
        .catch((error) => {
          console.error('Error removing watcher:', error);
          setSnackbarMessage('Failed to remove watcher');
          setSnackbarOpen(true);
        });
    }
  };



  if (loading || !currentIssue) {
    return <Typography>Loading issue details...</Typography>;
  }

  // Debug the currentIssue object
  console.log('Current Issue:', currentIssue);

  // Check for missing properties that might cause rendering errors
  if (!currentIssue.assignee) {
    console.log('Warning: assignee is missing');
  }
  if (!currentIssue.reporter) {
    console.log('Warning: reporter is missing');
  }
  if (!currentIssue.watchers) {
    console.log('Warning: watchers is missing');
  }
  if (!currentIssue.comments) {
    console.log('Warning: comments is missing');
  }
  if (!currentIssue.attachments) {
    console.log('Warning: attachments is missing');
  }

  const isWatching = user && currentIssue.watchers?.some(watcher => watcher.id === user.id);

  // Wrap the JSX in a try-catch block to catch rendering errors
  let renderedContent;
  try {
    renderedContent = (
      <Box>
      {/* Breadcrumbs Navigation */}
      <Paper
        sx={{
          p: 2,
          mb: 3,
          display: 'flex',
          alignItems: 'center',
          borderRadius: 2,
          boxShadow: '0 1px 5px rgba(0,0,0,0.05)',
          bgcolor: 'rgba(0,0,0,0.01)'
        }}
      >
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate(-1)}
          sx={{
            mr: 2,
            borderRadius: 2,
            borderColor: 'rgba(0,0,0,0.12)',
            '&:hover': {
              borderColor: 'primary.main',
              bgcolor: 'rgba(25, 118, 210, 0.04)'
            }
          }}
        >
          Back
        </Button>
        <Breadcrumbs
          aria-label="breadcrumb"
          separator={<NavigateNextIcon fontSize="small" />}
          sx={{
            '& .MuiBreadcrumbs-separator': {
              mx: 1,
              color: 'text.disabled'
            }
          }}
        >
          <Link
            underline="hover"
            color="inherit"
            sx={{
              display: 'flex',
              alignItems: 'center',
              cursor: 'pointer',
              borderRadius: 1,
              px: 1,
              py: 0.5,
              '&:hover': {
                bgcolor: 'rgba(0,0,0,0.04)'
              }
            }}
            onClick={() => navigate(ROUTES.APP.DASHBOARD)}
          >
            <HomeIcon sx={{ mr: 0.5, fontSize: '1rem' }} />
            Dashboard
          </Link>
          <Link
            underline="hover"
            color="inherit"
            sx={{
              display: 'flex',
              alignItems: 'center',
              cursor: 'pointer',
              borderRadius: 1,
              px: 1,
              py: 0.5,
              '&:hover': {
                bgcolor: 'rgba(0,0,0,0.04)'
              }
            }}
            onClick={() => navigate(ROUTES.APP.ISSUES.ROOT)}
          >
            <BugReportIcon sx={{ mr: 0.5, fontSize: '1rem' }} />
            Issues
          </Link>
          <Chip
            label={currentIssue.identifier}
            size="small"
            icon={<BugReportIcon fontSize="small" />}
            sx={{
              borderRadius: 1,
              bgcolor: 'primary.light',
              color: 'primary.contrastText',
              fontWeight: 'medium',
              '& .MuiChip-icon': {
                color: 'inherit'
              }
            }}
          />
        </Breadcrumbs>
      </Paper>

      <Paper sx={{ p: 3, mb: 3, borderRadius: 2, boxShadow: '0 2px 10px rgba(0,0,0,0.08)' }}>
        <Grid container spacing={2}>
          <Grid item xs>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Chip
                label={currentIssue.status.replace('_', ' ')}
                color={getStatusColor(currentIssue.status)}
                sx={{ mr: 1, fontWeight: 'bold' }}
              />
              <Typography variant="body2" color="text.secondary">
                in list <strong>BACKLOG</strong>
              </Typography>

              {/* Completion Date Badge */}
              {currentIssue.completionDate && (
                <Chip
                  icon={<CheckCircleIcon />}
                  label={`Completed: ${formatDate(currentIssue.completionDate)}`}
                  color="success"
                  variant="outlined"
                  sx={{ ml: 2, fontWeight: 'medium' }}
                />
              )}

              {/* Reopen Count Badge */}
              {currentIssue.reopenCount > 0 && (
                <Chip
                  icon={<HistoryIcon />}
                  label={`Reopened: ${currentIssue.reopenCount} ${currentIssue.reopenCount === 1 ? 'time' : 'times'}`}
                  color="warning"
                  variant="outlined"
                  sx={{ ml: 2, fontWeight: 'medium' }}
                />
              )}
            </Box>
            <Typography variant="h4" sx={{ display: 'flex', alignItems: 'center' }}>
              <BugReportIcon sx={{ mr: 1, opacity: 0.7 }} />
              {currentIssue.identifier}: {currentIssue.title}
            </Typography>
          </Grid>
          <Grid item>
            <Button
              variant="contained"
              color="primary"
              startIcon={<EditIcon />}
              onClick={handleEditIssue}
              sx={{ mr: 1, borderRadius: 2 }}
            >
              Edit
            </Button>
            <Button
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={handleOpenDeleteDialog}
              sx={{ borderRadius: 2 }}
            >
              Delete
            </Button>
          </Grid>
        </Grid>

        <Box sx={{ mt: 2, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          <Chip
            label={currentIssue.type.replace('_', ' ')}
            sx={{ borderRadius: 2 }}
          />
          <Chip
            label={`Priority: ${currentIssue.priority}`}
            sx={{
              borderRadius: 2,
              backgroundColor: getPriorityColor(currentIssue.priority),
              color: getContrastTextColor(getPriorityColor(currentIssue.priority)),
              fontWeight: 'medium'
            }}
          />
          <Chip
            label={`Severity: ${currentIssue.severity}`}
            sx={{
              borderRadius: 2,
              backgroundColor: getSeverityColor(currentIssue.severity),
              color: getContrastTextColor(getSeverityColor(currentIssue.severity)),
              fontWeight: 'medium'
            }}
          />
          {currentIssue.versionRelease && (
            <Chip
              label={`Version: ${currentIssue.versionRelease}`}
              sx={{ borderRadius: 2 }}
            />
          )}
          <Chip
            icon={<VisibilityIcon fontSize="small" />}
            label="Watching"
            variant="outlined"
            sx={{ borderRadius: 2, ml: 'auto' }}
          />
        </Box>

        <Grid container spacing={2} sx={{ mt: 2 }}>
          <Grid item xs={12} md={8}>
            <Box sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <DescriptionIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  Description
                </Typography>
              </Box>
              <Paper
                variant="outlined"
                sx={{
                  p: 2,
                  borderRadius: 2,
                  bgcolor: 'rgba(0,0,0,0.02)',
                  minHeight: '100px',
                  '&:hover': {
                    bgcolor: 'rgba(0,0,0,0.03)'
                  }
                }}
              >
                <Typography variant="body1" sx={{ whiteSpace: 'pre-line' }}>
                  {currentIssue.description || 'Add a more detailed description...'}
                </Typography>
              </Paper>

              {/* Issue Type Specific Fields */}
              <IssueTypeSpecificFields
                issue={currentIssue}
                issueType={currentIssue.type}
              />

              {/* Root Cause - show if it has a value */}
              {currentIssue.rootCause && (
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <BugReportIcon sx={{ mr: 1, color: 'text.secondary' }} />
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      Root Cause
                    </Typography>
                  </Box>
                  <Paper
                    variant="outlined"
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      bgcolor: 'rgba(255,193,7,0.05)',
                      borderColor: 'warning.main',
                      minHeight: '60px'
                    }}
                  >
                    <Typography variant="body1" sx={{ whiteSpace: 'pre-line' }}>
                      {currentIssue.rootCause}
                    </Typography>
                  </Paper>
                </Box>
              )}
            </Box>
          </Grid>

          <Grid item xs={12} md={4}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Paper
                variant="outlined"
                sx={{
                  p: 2,
                  borderRadius: 2,
                  border: '1px solid rgba(0, 0, 0, 0.12)'
                }}
              >
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
                  Details
                </Typography>
                <List dense disablePadding>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      <PersonIcon fontSize="small" color="action" />
                    </ListItemIcon>
                    <ListItemText
                      primary={<Typography variant="body2" color="text.secondary">Assignee</Typography>}
                      secondary={
                        <Chip
                          size="small"
                          label={currentIssue.assignee ? currentIssue.assignee.username : 'Unassigned'}
                          avatar={<Avatar sx={{ bgcolor: blue[500] }}>{currentIssue.assignee ? currentIssue.assignee.username[0] : 'U'}</Avatar>}
                          sx={{ mt: 0.5, borderRadius: 2 }}
                        />
                      }
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      <PersonIcon fontSize="small" color="action" />
                    </ListItemIcon>
                    <ListItemText
                      primary={<Typography variant="body2" color="text.secondary">Reporter</Typography>}
                      secondary={
                        <Chip
                          size="small"
                          label={currentIssue.reporter ? currentIssue.reporter.username : 'Unknown'}
                          avatar={<Avatar sx={{ bgcolor: green[500] }}>{currentIssue.reporter ? currentIssue.reporter.username[0] : 'U'}</Avatar>}
                          sx={{ mt: 0.5, borderRadius: 2 }}
                        />
                      }
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      <CalendarTodayIcon fontSize="small" color="action" />
                    </ListItemIcon>
                    <ListItemText
                      primary={<Typography variant="body2" color="text.secondary">Dates</Typography>}
                      secondary={
                        <Box sx={{ mt: 0.5 }}>
                          <Typography variant="body2">Created: {formatDate(currentIssue.createdAt)}</Typography>
                          <Typography variant="body2">Updated: {formatDate(currentIssue.updatedAt)}</Typography>
                          {currentIssue.devCompletionDate && (
                            <Typography variant="body2">Dev Completion: {formatDate(currentIssue.devCompletionDate)}</Typography>
                          )}
                          {currentIssue.qcCompletionDate && (
                            <Typography variant="body2">QC Completion: {formatDate(currentIssue.qcCompletionDate)}</Typography>
                          )}
                          {currentIssue.targetDate && (
                            <Typography variant="body2">Target: {formatDate(currentIssue.targetDate)}</Typography>
                          )}
                        </Box>
                      }
                    />
                  </ListItem>

                  {/* Separate Completion Date Item */}
                  {currentIssue.completionDate && (
                    <ListItem sx={{ px: 0 }}>
                      <ListItemIcon sx={{ minWidth: 36 }}>
                        <CheckCircleIcon fontSize="small" color="success" />
                      </ListItemIcon>
                      <ListItemText
                        primary={<Typography variant="body2" color="text.secondary">Completion Date</Typography>}
                        secondary={
                          <Box sx={{ mt: 0.5 }}>
                            <Typography
                              variant="body2"
                              sx={{
                                fontWeight: 'medium',
                                color: 'success.main'
                              }}
                            >
                              {formatDateTime(currentIssue.completionDate)}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                  )}
                  {currentIssue.parent && (
                    <ListItem sx={{ px: 0 }}>
                      <ListItemIcon sx={{ minWidth: 36 }}>
                        <LinkIcon fontSize="small" color="action" />
                      </ListItemIcon>
                      <ListItemText
                        primary={<Typography variant="body2" color="text.secondary">Parent Issue</Typography>}
                        secondary={
                          <Chip
                            size="small"
                            label={currentIssue.parent.identifier}
                            sx={{ mt: 0.5, borderRadius: 2 }}
                            onClick={() => navigate(ROUTES.APP.ISSUES.DETAIL(currentIssue.parent?.id!))}
                          />
                        }
                      />
                    </ListItem>
                  )}
                </List>
              </Paper>

              <Paper
                variant="outlined"
                sx={{
                  p: 2,
                  borderRadius: 2,
                  border: '1px solid rgba(0, 0, 0, 0.12)'
                }}
              >
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2 }}>
                  Actions
                </Typography>

                {/* Labels Section */}
                <Box sx={{ mb: 2 }}>
                  <LabelManager
                    issueId={currentIssue.id}
                    currentLabels={currentIssue.labels || []}
                    onLabelsChange={(labels) => {
                      // Refresh issue data to get updated labels
                      if (id) {
                        dispatch(fetchIssueById(parseInt(id)));
                      }
                    }}
                  />
                </Box>

                {/* Checklist Section */}
                <Box sx={{ mb: 2 }}>
                  <ChecklistManager
                    issueId={currentIssue.id}
                    onChecklistChange={() => {
                      // Refresh issue data to get updated checklist
                      if (id) {
                        dispatch(fetchIssueById(parseInt(id)));
                      }
                    }}
                  />
                </Box>


              </Paper>

              <Paper
                variant="outlined"
                sx={{
                  p: 2,
                  borderRadius: 2,
                  border: '1px solid rgba(0, 0, 0, 0.12)'
                }}
              >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                    Watchers ({currentIssue.watchers?.length || 0})
                  </Typography>
                  {!isWatching && (
                    <Button
                      size="small"
                      startIcon={<AddIcon />}
                      onClick={handleAddWatcher}
                      sx={{ borderRadius: 2 }}
                    >
                      Watch
                    </Button>
                  )}
                </Box>

                <List dense disablePadding>
                  {currentIssue.watchers?.map((watcher) => (
                    <ListItem
                      key={watcher.id}
                      sx={{ px: 0 }}
                      secondaryAction={
                        watcher.id === user?.id && (
                          <IconButton
                            edge="end"
                            onClick={() => handleRemoveWatcher(watcher.id)}
                            size="small"
                            sx={{
                              bgcolor: 'rgba(0,0,0,0.04)',
                              '&:hover': { bgcolor: 'rgba(0,0,0,0.08)' }
                            }}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        )
                      }
                    >
                      <ListItemAvatar sx={{ minWidth: 40 }}>
                        <Avatar sx={{ width: 28, height: 28, bgcolor: blue[400], fontSize: '0.875rem' }}>
                          {watcher.username.charAt(0).toUpperCase()}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={watcher.username}
                        primaryTypographyProps={{ variant: 'body2' }}
                      />
                    </ListItem>
                  ))}
                  {currentIssue.watchers?.length === 0 && (
                    <Typography variant="body2" color="text.secondary" sx={{ py: 1 }}>
                      No watchers yet
                    </Typography>
                  )}
                </List>
              </Paper>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      <Paper sx={{ mb: 3, borderRadius: 2, overflow: 'hidden' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="issue tabs"
            sx={{
              '& .MuiTab-root': {
                minHeight: '48px',
                fontWeight: 'medium',
                textTransform: 'none',
                fontSize: '0.95rem'
              },
              '& .Mui-selected': {
                fontWeight: 'bold'
              }
            }}
          >
            <Tab
              icon={<CommentIcon fontSize="small" />}
              iconPosition="start"
              label="Activity"
              id="issue-tab-0"
              aria-controls="issue-tabpanel-0"
            />
            <Tab
              icon={<AttachmentIcon fontSize="small" />}
              iconPosition="start"
              label="Attachments"
              id="issue-tab-1"
              aria-controls="issue-tabpanel-1"
            />
            <Tab
              icon={<HistoryIcon fontSize="small" />}
              iconPosition="start"
              label="History"
              id="issue-tab-2"
              aria-controls="issue-tabpanel-2"
            />
            {currentIssue.children?.length > 0 && (
              <Tab
                icon={<SubdirectoryArrowRightIcon fontSize="small" />}
                iconPosition="start"
                label="Sub-issues"
                id="issue-tab-3"
                aria-controls="issue-tabpanel-3"
              />
            )}
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Box sx={{ mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5 }}>
              <Avatar
                sx={{
                  width: 36,
                  height: 36,
                  bgcolor: blue[500],
                  fontSize: '1rem'
                }}
              >
                {user?.username?.[0]?.toUpperCase() || 'U'}
              </Avatar>
              <Box sx={{ flexGrow: 1 }}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  placeholder="Write a comment..."
                  value={commentText}
                  onChange={(e) => setCommentText(e.target.value)}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: blue[500],
                        borderWidth: 2
                      }
                    }
                  }}
                />
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
                  <Button
                    variant="contained"
                    endIcon={isSubmittingComment ? <CircularProgress size={16} color="inherit" /> : <SendIcon />}
                    onClick={handleCommentSubmit}
                    disabled={!commentText.trim() || isSubmittingComment}
                    sx={{ borderRadius: 2 }}
                  >
                    {isSubmittingComment ? 'Adding...' : 'Add Comment'}
                  </Button>
                </Box>
              </Box>
            </Box>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <ForumIcon sx={{ mr: 1, color: 'text.secondary' }} />
            <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
              Comments
            </Typography>
          </Box>

          <List>
            {currentIssue.comments?.length > 0 ? (
              currentIssue.comments.map((comment) => (
                <React.Fragment key={comment.id}>
                  <ListItem
                    alignItems="flex-start"
                    sx={{ px: 1 }}
                    secondaryAction={
                      comment.user.id === user?.id && (
                        <IconButton
                          edge="end"
                          onClick={() => handleDeleteComment(comment.id)}
                          size="small"
                          sx={{
                            bgcolor: 'rgba(0,0,0,0.04)',
                            '&:hover': { bgcolor: 'rgba(0,0,0,0.08)' }
                          }}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      )
                    }
                  >
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: comment.user.id === user?.id ? blue[500] : green[500] }}>
                        {comment.user.username.charAt(0).toUpperCase()}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                            {comment.user.username}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {formatDateTime(comment.createdAt)}
                          </Typography>
                        </Box>
                      }
                      secondary={
                        <Paper
                          variant="outlined"
                          sx={{
                            mt: 1,
                            p: 1.5,
                            borderRadius: 2,
                            bgcolor: 'rgba(0,0,0,0.02)',
                            borderColor: 'rgba(0,0,0,0.09)'
                          }}
                        >
                          <Typography
                            variant="body2"
                            color="text.primary"
                            sx={{ whiteSpace: 'pre-line' }}
                          >
                            {comment.content}
                          </Typography>
                        </Paper>
                      }
                    />
                  </ListItem>
                  <Divider variant="inset" component="li" sx={{ my: 1 }} />
                </React.Fragment>
              ))
            ) : (
              <Paper
                variant="outlined"
                sx={{
                  p: 3,
                  borderRadius: 2,
                  bgcolor: 'rgba(0,0,0,0.02)',
                  borderStyle: 'dashed',
                  textAlign: 'center'
                }}
              >
                <ChatBubbleOutlineIcon sx={{ fontSize: 40, color: 'text.disabled', mb: 1 }} />
                <Typography variant="body1" color="text.secondary">
                  No comments yet. Be the first to comment!
                </Typography>
              </Paper>
            )}
          </List>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Box sx={{ mb: 4 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <CloudUploadIcon sx={{ mr: 1, color: 'text.secondary' }} />
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                Upload Attachment
              </Typography>
            </Box>

            <Paper
              variant="outlined"
              sx={{
                p: 3,
                borderRadius: 2,
                borderStyle: 'dashed',
                borderColor: 'rgba(0,0,0,0.15)',
                bgcolor: 'rgba(0,0,0,0.01)',
                transition: 'all 0.2s',
                '&:hover': {
                  borderColor: 'primary.main',
                  bgcolor: 'rgba(25, 118, 210, 0.04)'
                }
              }}
            >
              <Grid container spacing={2} alignItems="center">
                <Grid item xs>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <TextField
                      type="file"
                      fullWidth
                      variant="outlined"
                      InputProps={{
                        startAdornment: <AttachmentIcon sx={{ mr: 1, color: 'primary.main' }} />,
                        sx: {
                          borderRadius: 2,
                          '& .MuiOutlinedInput-notchedOutline': {
                            borderColor: file ? 'primary.main' : 'rgba(0,0,0,0.15)'
                          }
                        }
                      }}
                      onChange={handleFileChange}
                    />
                  </Box>
                  {file && (
                    <Box sx={{ mt: 1, display: 'flex', alignItems: 'center' }}>
                      <CheckCircleIcon color="success" fontSize="small" sx={{ mr: 1 }} />
                      <Typography variant="body2" color="text.secondary">
                        {file.name} ({(file.size / 1024).toFixed(1)} KB) selected
                      </Typography>
                    </Box>
                  )}
                </Grid>
                <Grid item>
                  <Button
                    variant="contained"
                    startIcon={isUploadingAttachment ? <CircularProgress size={16} color="inherit" /> : <CloudUploadIcon />}
                    onClick={handleUploadAttachment}
                    disabled={!file || isUploadingAttachment}
                    sx={{
                      borderRadius: 2,
                      px: 3,
                      py: 1,
                      fontWeight: 'medium'
                    }}
                  >
                    {isUploadingAttachment ? 'Uploading...' : 'Upload'}
                  </Button>
                </Grid>
              </Grid>
            </Paper>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <FolderIcon sx={{ mr: 1, color: 'text.secondary' }} />
            <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
              Attachments
            </Typography>
          </Box>

          {currentIssue.attachments?.length > 0 ? (
            <Grid container spacing={2}>
              {currentIssue.attachments.map((attachment) => (
                <Grid item xs={12} sm={6} md={4} key={attachment.id}>
                  <Paper
                    variant="outlined"
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      transition: 'all 0.2s',
                      '&:hover': {
                        boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
                        borderColor: 'primary.main'
                      }
                    }}
                  >
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar
                          variant="rounded"
                          sx={{
                            bgcolor: 'primary.light',
                            width: 40,
                            height: 40,
                            mr: 1.5
                          }}
                        >
                          <InsertDriveFileIcon />
                        </Avatar>
                        <Box>
                          <Typography
                            variant="subtitle2"
                            sx={{
                              fontWeight: 'bold',
                              maxWidth: '180px',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap'
                            }}
                          >
                            {attachment.filename}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {formatDate(attachment.createdAt)}
                          </Typography>
                        </Box>
                      </Box>
                      {attachment.uploadedBy.id === user?.id && (
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteAttachment(attachment.id)}
                          sx={{
                            color: 'error.light',
                            bgcolor: 'rgba(0,0,0,0.04)',
                            '&:hover': {
                              bgcolor: 'rgba(211, 47, 47, 0.08)',
                              color: 'error.main'
                            }
                          }}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      )}
                    </Box>

                    <Divider sx={{ my: 1 }} />

                    <Box sx={{ mt: 'auto', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="caption" color="text.secondary">
                        By {attachment.uploadedBy.username}
                      </Typography>
                      <Button
                        size="small"
                        variant="outlined"
                        startIcon={<GetAppIcon />}
                        href={`/api/attachments/download/${attachment.id}`}
                        target="_blank"
                        sx={{
                          borderRadius: 2,
                          textTransform: 'none'
                        }}
                      >
                        Download
                      </Button>
                    </Box>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          ) : (
            <Paper
              variant="outlined"
              sx={{
                p: 3,
                borderRadius: 2,
                bgcolor: 'rgba(0,0,0,0.02)',
                borderStyle: 'dashed',
                textAlign: 'center'
              }}
            >
              <FolderOpenIcon sx={{ fontSize: 40, color: 'text.disabled', mb: 1 }} />
              <Typography variant="body1" color="text.secondary">
                No attachments yet. Upload files to share with the team.
              </Typography>
            </Paper>
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <HistoryIcon sx={{ mr: 1, color: 'text.secondary' }} />
            <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
              Issue History
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {/* Issue Created */}
            <Paper
              elevation={0}
              variant="outlined"
              sx={{
                p: 2,
                borderRadius: 2,
                borderColor: 'rgba(0,0,0,0.12)',
                position: 'relative',
                pl: 6
              }}
            >
              <Avatar
                sx={{
                  position: 'absolute',
                  left: 10,
                  top: 16,
                  bgcolor: 'primary.main',
                  width: 32,
                  height: 32
                }}
              >
                <AddCircleIcon fontSize="small" />
              </Avatar>
              <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                Issue Created
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {formatDateTime(currentIssue.createdAt)} by {currentIssue.reporter?.username || 'Unknown'}
              </Typography>
            </Paper>

            {/* Status Changed */}
            <Paper
              elevation={0}
              variant="outlined"
              sx={{
                p: 2,
                borderRadius: 2,
                borderColor: 'rgba(0,0,0,0.12)',
                position: 'relative',
                pl: 6
              }}
            >
              <Avatar
                sx={{
                  position: 'absolute',
                  left: 10,
                  top: 16,
                  bgcolor: 'info.main',
                  width: 32,
                  height: 32
                }}
              >
                <EditIcon fontSize="small" />
              </Avatar>
              <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                Status Changed
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {formatDateTime(currentIssue.updatedAt)}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                <Chip
                  label="NEW"
                  size="small"
                  sx={{ mr: 1, opacity: 0.7 }}
                  color="info"
                />
                <ArrowRightAltIcon sx={{ mx: 1, color: 'text.disabled' }} />
                <Chip
                  label={currentIssue.status}
                  size="small"
                  color={getStatusColor(currentIssue.status)}
                />
              </Box>
            </Paper>

            {/* Assignee Changed */}
            {currentIssue.assignee && (
              <Paper
                elevation={0}
                variant="outlined"
                sx={{
                  p: 2,
                  borderRadius: 2,
                  borderColor: 'rgba(0,0,0,0.12)',
                  position: 'relative',
                  pl: 6
                }}
              >
                <Avatar
                  sx={{
                    position: 'absolute',
                    left: 10,
                    top: 16,
                    bgcolor: 'success.main',
                    width: 32,
                    height: 32
                  }}
                >
                  <PersonIcon fontSize="small" />
                </Avatar>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                  Assignee Changed
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {new Date(currentIssue.updatedAt).toLocaleString()}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <Chip
                    label="Unassigned"
                    size="small"
                    sx={{ mr: 1, opacity: 0.7 }}
                  />
                  <ArrowRightAltIcon sx={{ mx: 1, color: 'text.disabled' }} />
                  <Chip
                    avatar={<Avatar sx={{ bgcolor: blue[500] }}>{currentIssue.assignee.username[0]}</Avatar>}
                    label={currentIssue.assignee.username}
                    size="small"
                  />
                </Box>
              </Paper>
            )}

            {/* Comments Added */}
            {currentIssue.comments && currentIssue.comments.length > 0 && (
              <Paper
                elevation={0}
                variant="outlined"
                sx={{
                  p: 2,
                  borderRadius: 2,
                  borderColor: 'rgba(0,0,0,0.12)',
                  position: 'relative',
                  pl: 6
                }}
              >
                <Avatar
                  sx={{
                    position: 'absolute',
                    left: 10,
                    top: 16,
                    bgcolor: 'warning.main',
                    width: 32,
                    height: 32
                  }}
                >
                  <CommentIcon fontSize="small" />
                </Avatar>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                  Comments Added
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {currentIssue.comments.length} comment(s) on this issue
                </Typography>
              </Paper>
            )}

            {/* Issue Completed */}
            {currentIssue.completionDate && (
              <Paper
                elevation={0}
                variant="outlined"
                sx={{
                  p: 2,
                  borderRadius: 2,
                  borderColor: 'rgba(0,0,0,0.12)',
                  bgcolor: 'success.light',
                  color: 'success.contrastText',
                  position: 'relative',
                  pl: 6,
                  mb: 2
                }}
              >
                <Avatar
                  sx={{
                    position: 'absolute',
                    left: 10,
                    top: 16,
                    bgcolor: 'success.dark',
                    width: 32,
                    height: 32
                  }}
                >
                  <CheckCircleIcon fontSize="small" />
                </Avatar>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                  Issue Completed
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  {new Date(currentIssue.completionDate).toLocaleString()}
                </Typography>
              </Paper>
            )}

            {/* Target Date Changes */}
            {currentIssue.targetDateChanges && currentIssue.targetDateChanges.length > 0 && (
              <Paper
                elevation={0}
                variant="outlined"
                sx={{
                  p: 2,
                  borderRadius: 2,
                  borderColor: 'rgba(0,0,0,0.12)',
                  bgcolor: 'info.light',
                  color: 'info.contrastText',
                  position: 'relative',
                  pl: 6
                }}
              >
                <Avatar
                  sx={{
                    position: 'absolute',
                    left: 10,
                    top: 16,
                    bgcolor: 'info.dark',
                    width: 32,
                    height: 32
                  }}
                >
                  <HistoryIcon fontSize="small" />
                </Avatar>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                  Target Date Changes ({currentIssue.targetDateChanges.length})
                </Typography>
                {currentIssue.targetDateChanges.map((change: any, index: number) => (
                  <Box key={index} sx={{ mt: 1, pl: 1, borderLeft: '2px solid rgba(255,255,255,0.3)' }}>
                    <Typography variant="body2" sx={{ opacity: 0.9, fontWeight: 'medium' }}>
                      Changed from {formatDate(change.oldDate)} to {formatDate(change.newDate)}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.8, mt: 0.5 }}>
                      Reason: {change.reason}
                    </Typography>
                    <Typography variant="caption" sx={{ opacity: 0.7, display: 'block', mt: 0.5 }}>
                      {formatDateTime(change.changedAt)} by {change.changedBy.username}
                    </Typography>
                  </Box>
                ))}
              </Paper>
            )}
          </Box>
        </TabPanel>

        {currentIssue.children?.length > 0 && (
          <TabPanel value={tabValue} index={3}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <SubdirectoryArrowRightIcon sx={{ mr: 1, color: 'text.secondary' }} />
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                Sub-issues
              </Typography>
            </Box>

            <Grid container spacing={2}>
              {currentIssue.children.map((child) => (
                <Grid item xs={12} md={6} key={child.id}>
                  <Paper
                    variant="outlined"
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      cursor: 'pointer',
                      transition: 'all 0.2s',
                      '&:hover': {
                        boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
                        borderColor: 'primary.main'
                      }
                    }}
                    onClick={() => navigate(ROUTES.APP.ISSUES.DETAIL(child.id))}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1.5 }}>
                      <Avatar
                        sx={{
                          bgcolor: 'primary.light',
                          width: 32,
                          height: 32,
                          fontSize: '0.875rem',
                          mr: 1.5
                        }}
                      >
                        {child.identifier.split('-')[0][0]}
                      </Avatar>
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography
                          variant="subtitle1"
                          sx={{
                            fontWeight: 'bold',
                            display: 'flex',
                            alignItems: 'center'
                          }}
                        >
                          <Chip
                            label={child.identifier}
                            size="small"
                            sx={{
                              mr: 1,
                              height: 20,
                              fontSize: '0.75rem',
                              bgcolor: 'primary.light',
                              color: 'primary.contrastText'
                            }}
                          />
                          {child.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                          {child.description ?
                            (child.description.length > 100 ?
                              child.description.substring(0, 100) + '...' :
                              child.description) :
                            'No description provided'}
                        </Typography>
                      </Box>
                    </Box>

                    <Divider sx={{ my: 1.5 }} />

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Chip
                          label={child.type.replace('_', ' ')}
                          size="small"
                          sx={{ borderRadius: 1, height: 24 }}
                        />
                        <Chip
                          label={child.status.replace('_', ' ')}
                          color={getStatusColor(child.status)}
                          size="small"
                          sx={{ borderRadius: 1, height: 24 }}
                        />
                        <Chip
                          label={child.priority}
                          color={getPriorityColor(child.priority)}
                          size="small"
                          sx={{ borderRadius: 1, height: 24 }}
                        />
                      </Box>

                      {child.assignee && (
                        <Tooltip title={`Assigned to ${child.assignee.username}`}>
                          <Avatar
                            sx={{
                              width: 24,
                              height: 24,
                              fontSize: '0.75rem',
                              bgcolor: blue[400]
                            }}
                          >
                            {child.assignee.username[0]}
                          </Avatar>
                        </Tooltip>
                      )}
                    </Box>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </TabPanel>
        )}
      </Paper>

      <Dialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
        PaperProps={{
          sx: {
            borderRadius: 3,
            overflow: 'visible',
            boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
            maxWidth: '450px'
          }
        }}
      >
        <DialogTitle
          sx={{
            pr: 6,
            py: 2,
            bgcolor: 'rgba(0,0,0,0.02)',
            borderBottom: '1px solid rgba(0,0,0,0.09)',
            display: 'flex',
            alignItems: 'center'
          }}
        >
          <DeleteIcon sx={{ mr: 1, color: 'error.main' }} />
          <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'error.main' }}>
            Delete Issue
          </Typography>
          <IconButton
            aria-label="close"
            onClick={handleCloseDeleteDialog}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
              bgcolor: 'rgba(0,0,0,0.04)',
              '&:hover': { bgcolor: 'rgba(0,0,0,0.08)' }
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Avatar sx={{ bgcolor: 'error.light', mr: 2 }}>
              <DeleteIcon />
            </Avatar>
            <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
              Are you sure you want to delete this issue?
            </Typography>
          </Box>
          <Typography variant="body2" color="text.secondary" sx={{ ml: 6 }}>
            This action cannot be undone. All associated comments, attachments, and history will be permanently removed.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ p: 2, px: 3, borderTop: '1px solid rgba(0, 0, 0, 0.12)' }}>
          <Button
            onClick={handleCloseDeleteDialog}
            sx={{ borderRadius: 2, fontWeight: 'medium' }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleDeleteIssue}
            color="error"
            variant="contained"
            sx={{ borderRadius: 2, fontWeight: 'medium' }}
          >
            Delete Issue
          </Button>
        </DialogActions>
      </Dialog>

      {/* Success Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbarOpen(false)}
          severity="success"
          variant="filled"
          icon={<CheckBoxIcon />}
          sx={{
            width: '100%',
            borderRadius: 2,
            boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
            '& .MuiAlert-icon': {
              alignItems: 'center'
            }
          }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
    );
  } catch (error) {
    console.error('Rendering error:', error);
    renderedContent = (
      <Box sx={{ p: 3 }}>
        <Typography variant="h6" color="error">An error occurred while rendering the issue details.</Typography>
        <Button
          variant="contained"
          onClick={() => window.location.reload()}
          sx={{ mt: 2 }}
        >
          Reload Page
        </Button>
      </Box>
    );
  }

  return renderedContent;
};

export default IssueDetail;
