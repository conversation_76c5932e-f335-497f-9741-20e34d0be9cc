package com.bugtracker.repository;

import com.bugtracker.dto.timesheet.WorkflowStatisticsDto;
import com.bugtracker.model.TimesheetApprovalWorkflow;
import com.bugtracker.model.TimesheetApprovalWorkflow.WorkflowStatus;
import com.bugtracker.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Timesheet Approval Workflow entities
 */
@Repository
public interface TimesheetApprovalWorkflowRepository extends JpaRepository<TimesheetApprovalWorkflow, Long> {

    /**
     * Find workflow by user and week start date
     */
    Optional<TimesheetApprovalWorkflow> findByUserAndWeekStartDate(User user, LocalDate weekStartDate);

    /**
     * Find workflow by user ID and week start date
     */
    Optional<TimesheetApprovalWorkflow> findByUserIdAndWeekStartDate(Long userId, LocalDate weekStartDate);

    /**
     * Find workflows by user
     */
    Page<TimesheetApprovalWorkflow> findByUserOrderByWeekStartDateDesc(User user, Pageable pageable);

    /**
     * Find workflows by user ID
     */
    Page<TimesheetApprovalWorkflow> findByUserIdOrderByWeekStartDateDesc(Long userId, Pageable pageable);

    /**
     * Find workflows by status
     */
    Page<TimesheetApprovalWorkflow> findByStatusOrderByWeekStartDateDesc(WorkflowStatus status, Pageable pageable);

    /**
     * Find workflows by user and status
     */
    Page<TimesheetApprovalWorkflow> findByUserAndStatusOrderByWeekStartDateDesc(User user, WorkflowStatus status, Pageable pageable);

    /**
     * Find workflows by date range
     */
    Page<TimesheetApprovalWorkflow> findByWeekStartDateBetweenOrderByWeekStartDateDesc(
            LocalDate startDate, LocalDate endDate, Pageable pageable);

    /**
     * Find workflows by user and date range
     */
    Page<TimesheetApprovalWorkflow> findByUserAndWeekStartDateBetweenOrderByWeekStartDateDesc(
            User user, LocalDate startDate, LocalDate endDate, Pageable pageable);

    /**
     * Find workflows pending approval
     */
    Page<TimesheetApprovalWorkflow> findByStatusOrderBySubmittedAtAsc(WorkflowStatus status, Pageable pageable);

    /**
     * Find workflows reviewed by user
     */
    Page<TimesheetApprovalWorkflow> findByReviewedByOrderByReviewedAtDesc(User reviewer, Pageable pageable);

    /**
     * Find workflows submitted by user
     */
    Page<TimesheetApprovalWorkflow> findBySubmittedByOrderBySubmittedAtDesc(User submitter, Pageable pageable);

    /**
     * Find current week workflow for user
     */
    @Query("SELECT w FROM TimesheetApprovalWorkflow w WHERE w.user = :user AND :currentDate BETWEEN w.weekStartDate AND w.weekEndDate")
    Optional<TimesheetApprovalWorkflow> findCurrentWeekWorkflow(@Param("user") User user, @Param("currentDate") LocalDate currentDate);

    /**
     * Find current week workflow for user by ID
     */
    @Query("SELECT w FROM TimesheetApprovalWorkflow w WHERE w.user.id = :userId AND :currentDate BETWEEN w.weekStartDate AND w.weekEndDate")
    Optional<TimesheetApprovalWorkflow> findCurrentWeekWorkflowByUserId(@Param("userId") Long userId, @Param("currentDate") LocalDate currentDate);

    /**
     * Find overdue submissions (draft status and week ended)
     */
    @Query("SELECT w FROM TimesheetApprovalWorkflow w WHERE w.status = 'DRAFT' AND w.weekEndDate < :currentDate ORDER BY w.weekEndDate ASC")
    Page<TimesheetApprovalWorkflow> findOverdueSubmissions(@Param("currentDate") LocalDate currentDate, Pageable pageable);

    /**
     * Find overdue approvals (submitted status and older than specified days)
     */
    @Query("SELECT w FROM TimesheetApprovalWorkflow w WHERE w.status = 'SUBMITTED' AND w.submittedAt < :cutoffDateTime ORDER BY w.submittedAt ASC")
    Page<TimesheetApprovalWorkflow> findOverdueApprovals(@Param("cutoffDateTime") LocalDateTime cutoffDateTime, Pageable pageable);

    /**
     * Count workflows by status
     */
    long countByStatus(WorkflowStatus status);

    /**
     * Count workflows by user and status
     */
    long countByUserAndStatus(User user, WorkflowStatus status);

    /**
     * Count workflows by status and date range
     */
    @Query("SELECT COUNT(w) FROM TimesheetApprovalWorkflow w WHERE w.status = :status AND w.weekStartDate BETWEEN :startDate AND :endDate")
    long countByStatusAndDateRange(@Param("status") WorkflowStatus status, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Get workflow statistics
     */
    @Query("SELECT new com.bugtracker.dto.timesheet.WorkflowStatisticsDto(w.status, COUNT(w), AVG(w.totalHours), SUM(w.totalHours)) " +
           "FROM TimesheetApprovalWorkflow w WHERE w.weekStartDate BETWEEN :startDate AND :endDate " +
           "GROUP BY w.status ORDER BY w.status")
    List<WorkflowStatisticsDto> getWorkflowStatistics(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Get user workflow statistics
     */
    @Query("SELECT w.user, COUNT(w), AVG(w.totalHours), SUM(w.totalHours), " +
           "SUM(CASE WHEN w.status = 'APPROVED' THEN 1 ELSE 0 END), " +
           "SUM(CASE WHEN w.status = 'REJECTED' THEN 1 ELSE 0 END) " +
           "FROM TimesheetApprovalWorkflow w WHERE w.weekStartDate BETWEEN :startDate AND :endDate " +
           "GROUP BY w.user ORDER BY SUM(w.totalHours) DESC")
    List<Object[]> getUserWorkflowStatistics(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Get reviewer statistics
     */
    @Query("SELECT w.reviewedBy, COUNT(w), AVG(w.totalHours) " +
           "FROM TimesheetApprovalWorkflow w WHERE w.reviewedBy IS NOT NULL AND w.weekStartDate BETWEEN :startDate AND :endDate " +
           "GROUP BY w.reviewedBy ORDER BY COUNT(w) DESC")
    List<Object[]> getReviewerStatistics(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Find workflows requiring attention (submitted and overdue)
     */
    @Query("SELECT w FROM TimesheetApprovalWorkflow w WHERE " +
           "(w.status = 'SUBMITTED' AND w.submittedAt < :overdueDateTime) OR " +
           "(w.status = 'DRAFT' AND w.weekEndDate < :currentDate) " +
           "ORDER BY w.submittedAt ASC, w.weekEndDate ASC")
    Page<TimesheetApprovalWorkflow> findWorkflowsRequiringAttention(
            @Param("overdueDateTime") LocalDateTime overdueDateTime, 
            @Param("currentDate") LocalDate currentDate, 
            Pageable pageable);

    /**
     * Find workflows by multiple users (for admin dashboard)
     */
    Page<TimesheetApprovalWorkflow> findByUserIdInOrderByWeekStartDateDesc(List<Long> userIds, Pageable pageable);

    /**
     * Find workflows with total hours above threshold
     */
    @Query("SELECT w FROM TimesheetApprovalWorkflow w WHERE w.totalHours > :threshold ORDER BY w.totalHours DESC")
    Page<TimesheetApprovalWorkflow> findWorkflowsAboveHoursThreshold(@Param("threshold") java.math.BigDecimal threshold, Pageable pageable);

    /**
     * Find workflows with total hours below threshold
     */
    @Query("SELECT w FROM TimesheetApprovalWorkflow w WHERE w.totalHours < :threshold AND w.status != 'DRAFT' ORDER BY w.totalHours ASC")
    Page<TimesheetApprovalWorkflow> findWorkflowsBelowHoursThreshold(@Param("threshold") java.math.BigDecimal threshold, Pageable pageable);

    /**
     * Get workflows for approval statistics calculation
     */
    @Query("SELECT w FROM TimesheetApprovalWorkflow w WHERE w.reviewedAt IS NOT NULL AND w.submittedAt IS NOT NULL")
    List<TimesheetApprovalWorkflow> findWorkflowsForApprovalTimeCalculation();

    /**
     * Get workflows for approval rate calculation
     */
    @Query("SELECT w FROM TimesheetApprovalWorkflow w WHERE w.status IN ('APPROVED', 'REJECTED')")
    List<TimesheetApprovalWorkflow> findWorkflowsForApprovalRateCalculation();

    /**
     * Find workflows by week containing date
     */
    @Query("SELECT w FROM TimesheetApprovalWorkflow w WHERE :date BETWEEN w.weekStartDate AND w.weekEndDate ORDER BY w.user.username")
    List<TimesheetApprovalWorkflow> findWorkflowsByWeekContainingDate(@Param("date") LocalDate date);

    /**
     * Check if workflow exists for user and week
     */
    boolean existsByUserAndWeekStartDate(User user, LocalDate weekStartDate);

    /**
     * Check if workflow exists for user ID and week
     */
    boolean existsByUserIdAndWeekStartDate(Long userId, LocalDate weekStartDate);

    /**
     * Delete workflows older than specified date (for cleanup)
     */
    void deleteByWeekStartDateBefore(LocalDate cutoffDate);

    /**
     * Find latest workflow for user
     */
    Optional<TimesheetApprovalWorkflow> findFirstByUserOrderByWeekStartDateDesc(User user);

    /**
     * Find latest workflow for user by ID
     */
    Optional<TimesheetApprovalWorkflow> findFirstByUserIdOrderByWeekStartDateDesc(Long userId);

    /**
     * Find workflows needing total hours update
     */
    @Query("SELECT w FROM TimesheetApprovalWorkflow w WHERE w.status = 'DRAFT' ORDER BY w.weekStartDate DESC")
    List<TimesheetApprovalWorkflow> findDraftWorkflowsForUpdate();
}
