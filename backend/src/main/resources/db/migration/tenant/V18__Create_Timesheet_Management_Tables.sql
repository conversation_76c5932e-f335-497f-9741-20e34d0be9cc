-- V18__Create_Timesheet_Management_Tables.sql
-- Create comprehensive timesheet management tables for user-friendly time tracking

-- Create timesheet_entries table for daily time logging
CREATE TABLE IF NOT EXISTS timesheet_entries (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    issue_id BIGINT NOT NULL,
    entry_date DATE NOT NULL,
    hours_spent DECIMAL(5,2) NOT NULL CHECK (hours_spent > 0 AND hours_spent <= 24),
    description TEXT NOT NULL,
    activity_type VARCHAR(50) NOT NULL DEFAULT 'DEVELOPMENT',
    billable BOOLEAN NOT NULL DEFAULT TRUE,
    is_approved BOOLEAN NOT NULL DEFAULT FALSE,
    approved_by BIGIN<PERSON>,
    approved_at TIMESTAMP,
    approval_notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOR<PERSON><PERSON><PERSON> KEY (issue_id) REFERENCES issues(id) ON DELETE CASCADE,
    FOREI<PERSON><PERSON> KEY (approved_by) REFERENCES users(id),
    UNIQUE (user_id, issue_id, entry_date, activity_type)
);

-- Create timesheet_approval_workflow table
CREATE TABLE IF NOT EXISTS timesheet_approval_workflow (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    week_start_date DATE NOT NULL,
    week_end_date DATE NOT NULL,
    total_hours DECIMAL(6,2) NOT NULL DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT 'DRAFT', -- DRAFT, SUBMITTED, APPROVED, REJECTED
    submitted_at TIMESTAMP,
    submitted_by BIGINT,
    reviewed_at TIMESTAMP,
    reviewed_by BIGINT,
    review_notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (submitted_by) REFERENCES users(id),
    FOREIGN KEY (reviewed_by) REFERENCES users(id),
    UNIQUE (user_id, week_start_date)
);

-- Create timesheet_permissions table for granular access control
CREATE TABLE IF NOT EXISTS timesheet_permissions (
    id SERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    permission_type VARCHAR(50) NOT NULL, -- VIEW_OWN, EDIT_OWN, VIEW_ALL, EDIT_ALL, APPROVE, EXPORT
    granted_by BIGINT NOT NULL,
    granted_at TIMESTAMP NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    notes TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id),
    UNIQUE (user_id, permission_type)
);

-- Create timesheet_export_logs table for audit trail
CREATE TABLE IF NOT EXISTS timesheet_export_logs (
    id SERIAL PRIMARY KEY,
    exported_by BIGINT NOT NULL,
    export_type VARCHAR(20) NOT NULL, -- CSV, EXCEL, PDF
    date_range_start DATE NOT NULL,
    date_range_end DATE NOT NULL,
    user_filter JSONB, -- JSON array of user IDs if filtered
    issue_filter JSONB, -- JSON array of issue IDs if filtered
    total_records INTEGER NOT NULL DEFAULT 0,
    file_path VARCHAR(500),
    export_status VARCHAR(20) NOT NULL DEFAULT 'PENDING', -- PENDING, COMPLETED, FAILED
    error_message TEXT,
    exported_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (exported_by) REFERENCES users(id)
);

-- Create timesheet_analytics_cache table for performance
CREATE TABLE IF NOT EXISTS timesheet_analytics_cache (
    id SERIAL PRIMARY KEY,
    cache_key VARCHAR(200) NOT NULL UNIQUE,
    cache_data JSONB NOT NULL,
    cache_type VARCHAR(50) NOT NULL, -- USER_SUMMARY, ISSUE_SUMMARY, WEEKLY_REPORT, MONTHLY_REPORT
    user_id BIGINT,
    date_range_start DATE,
    date_range_end DATE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create indexes for optimal performance
CREATE INDEX IF NOT EXISTS idx_timesheet_entries_user_date ON timesheet_entries(user_id, entry_date DESC);
CREATE INDEX IF NOT EXISTS idx_timesheet_entries_issue_date ON timesheet_entries(issue_id, entry_date DESC);
CREATE INDEX IF NOT EXISTS idx_timesheet_entries_date_range ON timesheet_entries(entry_date);
CREATE INDEX IF NOT EXISTS idx_timesheet_entries_activity_type ON timesheet_entries(activity_type);
CREATE INDEX IF NOT EXISTS idx_timesheet_entries_billable ON timesheet_entries(billable);
CREATE INDEX IF NOT EXISTS idx_timesheet_entries_approval_status ON timesheet_entries(is_approved);

CREATE INDEX IF NOT EXISTS idx_timesheet_workflow_user_week ON timesheet_approval_workflow(user_id, week_start_date DESC);
CREATE INDEX IF NOT EXISTS idx_timesheet_workflow_status ON timesheet_approval_workflow(status);
CREATE INDEX IF NOT EXISTS idx_timesheet_workflow_reviewer ON timesheet_approval_workflow(reviewed_by);

CREATE INDEX IF NOT EXISTS idx_timesheet_permissions_user ON timesheet_permissions(user_id, is_active);
CREATE INDEX IF NOT EXISTS idx_timesheet_permissions_type ON timesheet_permissions(permission_type, is_active);

CREATE INDEX IF NOT EXISTS idx_timesheet_export_logs_user ON timesheet_export_logs(exported_by, exported_at DESC);
CREATE INDEX IF NOT EXISTS idx_timesheet_export_logs_date_range ON timesheet_export_logs(date_range_start, date_range_end);

CREATE INDEX IF NOT EXISTS idx_timesheet_analytics_cache_key ON timesheet_analytics_cache(cache_key);
CREATE INDEX IF NOT EXISTS idx_timesheet_analytics_cache_expires ON timesheet_analytics_cache(expires_at);
CREATE INDEX IF NOT EXISTS idx_timesheet_analytics_cache_user_type ON timesheet_analytics_cache(user_id, cache_type);

-- Create database views for efficient reporting
CREATE OR REPLACE VIEW timesheet_daily_summary AS
SELECT 
    te.user_id,
    u.username,
    u.first_name,
    u.last_name,
    te.entry_date,
    COUNT(te.id) as total_entries,
    SUM(te.hours_spent) as total_hours,
    SUM(CASE WHEN te.billable THEN te.hours_spent ELSE 0 END) as billable_hours,
    SUM(CASE WHEN NOT te.billable THEN te.hours_spent ELSE 0 END) as non_billable_hours,
    COUNT(DISTINCT te.issue_id) as unique_issues,
    COUNT(CASE WHEN te.is_approved THEN 1 END) as approved_entries,
    COUNT(CASE WHEN NOT te.is_approved THEN 1 END) as pending_entries
FROM timesheet_entries te
JOIN users u ON te.user_id = u.id
GROUP BY te.user_id, u.username, u.first_name, u.last_name, te.entry_date;

CREATE OR REPLACE VIEW timesheet_weekly_summary AS
SELECT 
    te.user_id,
    u.username,
    u.first_name,
    u.last_name,
    DATE_TRUNC('week', te.entry_date) as week_start,
    DATE_TRUNC('week', te.entry_date) + INTERVAL '6 days' as week_end,
    COUNT(te.id) as total_entries,
    SUM(te.hours_spent) as total_hours,
    SUM(CASE WHEN te.billable THEN te.hours_spent ELSE 0 END) as billable_hours,
    COUNT(DISTINCT te.issue_id) as unique_issues,
    COUNT(DISTINCT te.entry_date) as days_worked
FROM timesheet_entries te
JOIN users u ON te.user_id = u.id
GROUP BY te.user_id, u.username, u.first_name, u.last_name, DATE_TRUNC('week', te.entry_date);

CREATE OR REPLACE VIEW timesheet_issue_summary AS
SELECT 
    te.issue_id,
    i.identifier as issue_identifier,
    i.title as issue_title,
    i.type as issue_type,
    i.status as issue_status,
    i.priority as issue_priority,
    COUNT(te.id) as total_entries,
    COUNT(DISTINCT te.user_id) as unique_users,
    SUM(te.hours_spent) as total_hours,
    AVG(te.hours_spent) as avg_hours_per_entry,
    MIN(te.entry_date) as first_entry_date,
    MAX(te.entry_date) as last_entry_date
FROM timesheet_entries te
JOIN issues i ON te.issue_id = i.id
GROUP BY te.issue_id, i.identifier, i.title, i.type, i.status, i.priority;

-- Insert default timesheet permissions for admin role
INSERT INTO timesheet_permissions (user_id, permission_type, granted_by, notes)
SELECT 
    u.id,
    perm.permission_type,
    u.id,
    'Default admin permissions'
FROM users u
CROSS JOIN (
    VALUES 
        ('VIEW_OWN'),
        ('EDIT_OWN'),
        ('VIEW_ALL'),
        ('EDIT_ALL'),
        ('APPROVE'),
        ('EXPORT')
) AS perm(permission_type)
WHERE EXISTS (
    SELECT 1 FROM user_roles ur 
    JOIN roles r ON ur.role_id = r.id 
    WHERE ur.user_id = u.id AND r.name = 'ROLE_ADMIN'
)
ON CONFLICT (user_id, permission_type) DO NOTHING;

-- Insert default timesheet permissions for regular users
INSERT INTO timesheet_permissions (user_id, permission_type, granted_by, notes)
SELECT 
    u.id,
    perm.permission_type,
    (SELECT id FROM users WHERE EXISTS (
        SELECT 1 FROM user_roles ur 
        JOIN roles r ON ur.role_id = r.id 
        WHERE ur.user_id = users.id AND r.name = 'ROLE_ADMIN'
    ) LIMIT 1),
    'Default user permissions'
FROM users u
CROSS JOIN (
    VALUES 
        ('VIEW_OWN'),
        ('EDIT_OWN')
) AS perm(permission_type)
WHERE NOT EXISTS (
    SELECT 1 FROM user_roles ur 
    JOIN roles r ON ur.role_id = r.id 
    WHERE ur.user_id = u.id AND r.name = 'ROLE_ADMIN'
)
ON CONFLICT (user_id, permission_type) DO NOTHING;

-- Add comments for documentation
COMMENT ON TABLE timesheet_entries IS 'Daily timesheet entries for user-friendly time logging';
COMMENT ON TABLE timesheet_approval_workflow IS 'Weekly timesheet approval workflow management';
COMMENT ON TABLE timesheet_permissions IS 'Granular permissions for timesheet access control';
COMMENT ON TABLE timesheet_export_logs IS 'Audit trail for timesheet data exports';
COMMENT ON TABLE timesheet_analytics_cache IS 'Performance cache for timesheet analytics';

COMMENT ON VIEW timesheet_daily_summary IS 'Daily aggregated timesheet data per user';
COMMENT ON VIEW timesheet_weekly_summary IS 'Weekly aggregated timesheet data per user';
COMMENT ON VIEW timesheet_issue_summary IS 'Aggregated timesheet data per issue';
