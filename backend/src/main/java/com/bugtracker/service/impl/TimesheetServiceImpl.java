package com.bugtracker.service.impl;

import com.bugtracker.model.*;
import com.bugtracker.repository.*;
import com.bugtracker.service.TimesheetService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Implementation of TimesheetService
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class TimesheetServiceImpl implements TimesheetService {

    private final TimesheetEntryRepository timesheetEntryRepository;
    private final TimesheetApprovalWorkflowRepository workflowRepository;
    private final UserRepository userRepository;
    private final IssueRepository issueRepository;
    private final RoleRepository roleRepository;

    // ===== Timesheet Entry Management =====

    @Override
    public TimesheetEntry createTimesheetEntry(Long userId, Long issueId, LocalDate entryDate,
                                              BigDecimal hoursSpent, String description,
                                              String activityType, Boolean billable) {
        log.debug("Creating timesheet entry for user: {}, issue: {}, date: {}", userId, issueId, entryDate);

        // Validate input
        validateTimesheetEntry(userId, issueId, entryDate, hoursSpent, description, activityType);

        // Get user and issue
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));
        Issue issue = issueRepository.findById(issueId)
                .orElseThrow(() -> new RuntimeException("Issue not found with id: " + issueId));

        // Check for duplicate entry
        if (hasDuplicateEntry(userId, issueId, entryDate, activityType)) {
            throw new IllegalArgumentException("Duplicate timesheet entry for the same user, issue, date, and activity type");
        }

        // Create timesheet entry
        TimesheetEntry entry = TimesheetEntry.builder()
                .user(user)
                .issue(issue)
                .entryDate(entryDate)
                .hoursSpent(hoursSpent)
                .description(description)
                .activityType(activityType)
                .billable(billable != null ? billable : true)
                .isApproved(false)
                .build();

        entry.validate();
        entry = timesheetEntryRepository.save(entry);

        // Update workflow total hours
        updateWorkflowTotalHoursForDate(userId, entryDate);

        log.info("Created timesheet entry with ID: {} for user: {}", entry.getId(), userId);
        return entry;
    }

    @Override
    public TimesheetEntry updateTimesheetEntry(Long entryId, BigDecimal hoursSpent,
                                              String description, String activityType, Boolean billable) {
        log.debug("Updating timesheet entry: {}", entryId);

        TimesheetEntry entry = timesheetEntryRepository.findById(entryId)
                .orElseThrow(() -> new RuntimeException("Timesheet entry not found with id: " + entryId));

        // Check if entry can be edited
        if (!entry.isEditable()) {
            throw new IllegalStateException("Timesheet entry cannot be edited. Only current day entries can be modified.");
        }

        // Update fields
        if (hoursSpent != null) {
            entry.setHoursSpent(hoursSpent);
        }
        if (description != null) {
            entry.setDescription(description);
        }
        if (activityType != null) {
            entry.setActivityType(activityType);
        }
        if (billable != null) {
            entry.setBillable(billable);
        }

        entry.validate();
        entry = timesheetEntryRepository.save(entry);

        // Update workflow total hours
        updateWorkflowTotalHoursForDate(entry.getUser().getId(), entry.getEntryDate());

        log.info("Updated timesheet entry: {}", entryId);
        return entry;
    }

    @Override
    public void deleteTimesheetEntry(Long entryId) {
        log.debug("Deleting timesheet entry: {}", entryId);

        TimesheetEntry entry = timesheetEntryRepository.findById(entryId)
                .orElseThrow(() -> new RuntimeException("Timesheet entry not found with id: " + entryId));

        // Check if entry can be edited
        if (!entry.isEditable()) {
            throw new IllegalStateException("Timesheet entry cannot be deleted. Only current day entries can be modified.");
        }

        Long userId = entry.getUser().getId();
        LocalDate entryDate = entry.getEntryDate();

        timesheetEntryRepository.delete(entry);

        // Update workflow total hours
        updateWorkflowTotalHoursForDate(userId, entryDate);

        log.info("Deleted timesheet entry: {}", entryId);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<TimesheetEntry> getTimesheetEntryById(Long entryId) {
        return timesheetEntryRepository.findById(entryId);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TimesheetEntry> getTimesheetEntriesByUser(Long userId, Pageable pageable) {
        return timesheetEntryRepository.findByUserIdOrderByEntryDateDescCreatedAtDesc(userId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TimesheetEntry> getTimesheetEntriesByUserAndDateRange(Long userId, LocalDate startDate,
                                                                     LocalDate endDate, Pageable pageable) {
        return timesheetEntryRepository.findByUserIdAndEntryDateBetweenOrderByEntryDateDescCreatedAtDesc(
                userId, startDate, endDate, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TimesheetEntry> getTodaysTimesheetEntries(Long userId) {
        return timesheetEntryRepository.findByUserIdAndEntryDateOrderByCreatedAtDesc(userId, LocalDate.now());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TimesheetEntry> getTimesheetEntriesByIssue(Long issueId, Pageable pageable) {
        return timesheetEntryRepository.findByIssueIdOrderByEntryDateDescCreatedAtDesc(issueId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TimesheetEntry> getAllTimesheetEntries(Pageable pageable) {
        return timesheetEntryRepository.findAll(pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TimesheetEntry> getTimesheetEntriesByDateRange(LocalDate startDate, LocalDate endDate, Pageable pageable) {
        return timesheetEntryRepository.findByEntryDateBetweenOrderByEntryDateDescCreatedAtDesc(
                startDate, endDate, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TimesheetEntry> getPendingApprovalEntries(Pageable pageable) {
        return timesheetEntryRepository.findByIsApprovedFalseOrderByEntryDateDescCreatedAtDesc(pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TimesheetEntry> getPendingApprovalEntriesByUser(Long userId, Pageable pageable) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));
        return timesheetEntryRepository.findByUserAndIsApprovedFalseOrderByEntryDateDescCreatedAtDesc(user, pageable);
    }

    // ===== Timesheet Approval Management =====

    @Override
    public TimesheetEntry approveTimesheetEntry(Long entryId, Long approverId, String notes) {
        log.debug("Approving timesheet entry: {} by user: {}", entryId, approverId);

        TimesheetEntry entry = timesheetEntryRepository.findById(entryId)
                .orElseThrow(() -> new RuntimeException("Timesheet entry not found with id: " + entryId));

        User approver = userRepository.findById(approverId)
                .orElseThrow(() -> new RuntimeException("Approver not found with id: " + approverId));

        // Check if user can approve
        if (!canApproveTimesheetEntries(approverId)) {
            throw new IllegalStateException("User does not have permission to approve timesheet entries");
        }

        entry.approve(approver, notes);
        entry = timesheetEntryRepository.save(entry);

        log.info("Approved timesheet entry: {} by user: {}", entryId, approverId);
        return entry;
    }

    @Override
    public TimesheetEntry rejectTimesheetEntry(Long entryId, String notes) {
        log.debug("Rejecting timesheet entry: {}", entryId);

        TimesheetEntry entry = timesheetEntryRepository.findById(entryId)
                .orElseThrow(() -> new RuntimeException("Timesheet entry not found with id: " + entryId));

        entry.reject(notes);
        entry = timesheetEntryRepository.save(entry);

        log.info("Rejected timesheet entry: {}", entryId);
        return entry;
    }

    @Override
    public List<TimesheetEntry> bulkApproveTimesheetEntries(List<Long> entryIds, Long approverId, String notes) {
        log.debug("Bulk approving {} timesheet entries by user: {}", entryIds.size(), approverId);

        // Check if user can approve
        if (!canApproveTimesheetEntries(approverId)) {
            throw new IllegalStateException("User does not have permission to approve timesheet entries");
        }

        User approver = userRepository.findById(approverId)
                .orElseThrow(() -> new RuntimeException("Approver not found with id: " + approverId));

        List<TimesheetEntry> entries = timesheetEntryRepository.findAllById(entryIds);
        
        for (TimesheetEntry entry : entries) {
            entry.approve(approver, notes);
        }

        entries = timesheetEntryRepository.saveAll(entries);

        log.info("Bulk approved {} timesheet entries by user: {}", entries.size(), approverId);
        return entries;
    }

    @Override
    public List<TimesheetEntry> bulkRejectTimesheetEntries(List<Long> entryIds, String notes) {
        log.debug("Bulk rejecting {} timesheet entries", entryIds.size());

        List<TimesheetEntry> entries = timesheetEntryRepository.findAllById(entryIds);
        
        for (TimesheetEntry entry : entries) {
            entry.reject(notes);
        }

        entries = timesheetEntryRepository.saveAll(entries);

        log.info("Bulk rejected {} timesheet entries", entries.size());
        return entries;
    }

    // ===== Helper Methods =====

    private void updateWorkflowTotalHoursForDate(Long userId, LocalDate entryDate) {
        LocalDate weekStartDate = TimesheetApprovalWorkflow.getWeekStartDate(entryDate);
        Optional<TimesheetApprovalWorkflow> workflowOpt = workflowRepository.findByUserIdAndWeekStartDate(userId, weekStartDate);
        
        if (workflowOpt.isPresent()) {
            updateWorkflowTotalHours(workflowOpt.get().getId());
        }
    }

    // ===== Validation and Permissions =====

    @Override
    @Transactional(readOnly = true)
    public boolean canEditTimesheetEntry(Long userId, Long entryId) {
        Optional<TimesheetEntry> entryOpt = timesheetEntryRepository.findById(entryId);
        if (entryOpt.isEmpty()) {
            return false;
        }

        TimesheetEntry entry = entryOpt.get();
        
        // User can only edit their own entries
        if (!entry.getUser().getId().equals(userId)) {
            return false;
        }

        // Only current day entries can be edited
        return entry.isEditable();
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canApproveTimesheetEntries(Long userId) {
        User user = userRepository.findById(userId).orElse(null);
        if (user == null) {
            return false;
        }

        // Check if user has admin role
        return user.getRoles().stream()
                .anyMatch(role -> "ROLE_ADMIN".equals(role.getName()));
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canViewAllTimesheets(Long userId) {
        return canApproveTimesheetEntries(userId); // Same permission as approval for now
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canExportTimesheetData(Long userId) {
        return canApproveTimesheetEntries(userId); // Same permission as approval for now
    }

    @Override
    public void validateTimesheetEntry(Long userId, Long issueId, LocalDate entryDate,
                                      BigDecimal hoursSpent, String description, String activityType) {
        if (userId == null) {
            throw new IllegalArgumentException("User ID is required");
        }
        if (issueId == null) {
            throw new IllegalArgumentException("Issue ID is required");
        }
        if (entryDate == null) {
            throw new IllegalArgumentException("Entry date is required");
        }
        if (entryDate.isAfter(LocalDate.now())) {
            throw new IllegalArgumentException("Cannot log time for future dates");
        }
        if (hoursSpent == null || hoursSpent.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Hours spent must be greater than 0");
        }
        if (hoursSpent.compareTo(BigDecimal.valueOf(24)) > 0) {
            throw new IllegalArgumentException("Hours spent cannot exceed 24 hours per day");
        }
        if (description == null || description.trim().isEmpty()) {
            throw new IllegalArgumentException("Description is required");
        }
        if (activityType == null || activityType.trim().isEmpty()) {
            throw new IllegalArgumentException("Activity type is required");
        }
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasDuplicateEntry(Long userId, Long issueId, LocalDate entryDate, String activityType) {
        User user = userRepository.findById(userId).orElse(null);
        Issue issue = issueRepository.findById(issueId).orElse(null);
        
        if (user == null || issue == null) {
            return false;
        }

        return timesheetEntryRepository.existsByUserAndIssueAndEntryDateAndActivityType(user, issue, entryDate, activityType);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TimesheetEntry> getConflictingEntries(Long userId, Long issueId, LocalDate entryDate, String activityType) {
        User user = userRepository.findById(userId).orElse(null);
        Issue issue = issueRepository.findById(issueId).orElse(null);
        
        if (user == null || issue == null) {
            return Collections.emptyList();
        }

        Optional<TimesheetEntry> conflictingEntry = timesheetEntryRepository.findByUserAndIssueAndEntryDateAndActivityType(
                user, issue, entryDate, activityType);
        
        return conflictingEntry.map(List::of).orElse(Collections.emptyList());
    }

    // ===== Workflow Management =====

    @Override
    public TimesheetApprovalWorkflow getOrCreateWorkflow(Long userId, LocalDate weekDate) {
        LocalDate weekStartDate = TimesheetApprovalWorkflow.getWeekStartDate(weekDate);

        Optional<TimesheetApprovalWorkflow> existingWorkflow = workflowRepository.findByUserIdAndWeekStartDate(userId, weekStartDate);

        if (existingWorkflow.isPresent()) {
            return existingWorkflow.get();
        }

        // Create new workflow
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));

        TimesheetApprovalWorkflow workflow = TimesheetApprovalWorkflow.createForWeek(user, weekDate);
        workflow = workflowRepository.save(workflow);

        // Calculate initial total hours
        updateWorkflowTotalHours(workflow.getId());

        log.info("Created new workflow for user: {} and week: {}", userId, weekStartDate);
        return workflow;
    }

    @Override
    public TimesheetApprovalWorkflow submitTimesheetForApproval(Long userId, LocalDate weekStartDate) {
        TimesheetApprovalWorkflow workflow = workflowRepository.findByUserIdAndWeekStartDate(userId, weekStartDate)
                .orElseThrow(() -> new RuntimeException("Workflow not found for user: " + userId + " and week: " + weekStartDate));

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));

        workflow.submit(user);
        workflow = workflowRepository.save(workflow);

        log.info("Submitted timesheet for approval: workflow ID {}", workflow.getId());
        return workflow;
    }

    @Override
    public TimesheetApprovalWorkflow approveTimesheetWorkflow(Long workflowId, Long reviewerId, String notes) {
        TimesheetApprovalWorkflow workflow = workflowRepository.findById(workflowId)
                .orElseThrow(() -> new RuntimeException("Workflow not found with id: " + workflowId));

        User reviewer = userRepository.findById(reviewerId)
                .orElseThrow(() -> new RuntimeException("Reviewer not found with id: " + reviewerId));

        workflow.approve(reviewer, notes);
        workflow = workflowRepository.save(workflow);

        log.info("Approved timesheet workflow: {} by reviewer: {}", workflowId, reviewerId);
        return workflow;
    }

    @Override
    public TimesheetApprovalWorkflow rejectTimesheetWorkflow(Long workflowId, Long reviewerId, String notes) {
        TimesheetApprovalWorkflow workflow = workflowRepository.findById(workflowId)
                .orElseThrow(() -> new RuntimeException("Workflow not found with id: " + workflowId));

        User reviewer = userRepository.findById(reviewerId)
                .orElseThrow(() -> new RuntimeException("Reviewer not found with id: " + reviewerId));

        workflow.reject(reviewer, notes);
        workflow = workflowRepository.save(workflow);

        log.info("Rejected timesheet workflow: {} by reviewer: {}", workflowId, reviewerId);
        return workflow;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TimesheetApprovalWorkflow> getWorkflowsByUser(Long userId, Pageable pageable) {
        return workflowRepository.findByUserIdOrderByWeekStartDateDesc(userId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TimesheetApprovalWorkflow> getWorkflowsPendingApproval(Pageable pageable) {
        return workflowRepository.findByStatusOrderBySubmittedAtAsc(TimesheetApprovalWorkflow.WorkflowStatus.SUBMITTED, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<TimesheetApprovalWorkflow> getCurrentWeekWorkflow(Long userId) {
        return workflowRepository.findCurrentWeekWorkflowByUserId(userId, LocalDate.now());
    }

    // ===== Analytics and Reporting =====

    @Override
    @Transactional(readOnly = true)
    public BigDecimal getTotalHoursByUser(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));
        return timesheetEntryRepository.calculateTotalHoursByUser(user);
    }

    @Override
    @Transactional(readOnly = true)
    public BigDecimal getTotalHoursByUserAndDateRange(Long userId, LocalDate startDate, LocalDate endDate) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));
        return timesheetEntryRepository.calculateTotalHoursByUserAndDateRange(user, startDate, endDate);
    }

    @Override
    @Transactional(readOnly = true)
    public BigDecimal getTotalHoursByIssue(Long issueId) {
        Issue issue = issueRepository.findById(issueId)
                .orElseThrow(() -> new RuntimeException("Issue not found with id: " + issueId));
        return timesheetEntryRepository.calculateTotalHoursByIssue(issue);
    }

    @Override
    @Transactional(readOnly = true)
    public BigDecimal getTotalBillableHoursByUser(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));
        return timesheetEntryRepository.calculateTotalBillableHoursByUser(user);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<LocalDate, Map<String, Object>> getDailySummaryByUser(Long userId, LocalDate startDate, LocalDate endDate) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));

        List<Object[]> results = timesheetEntryRepository.getDailySummaryByUser(user, startDate, endDate);
        Map<LocalDate, Map<String, Object>> summary = new LinkedHashMap<>();

        for (Object[] result : results) {
            LocalDate date = (LocalDate) result[0];
            Long entryCount = (Long) result[1];
            BigDecimal totalHours = (BigDecimal) result[2];
            BigDecimal billableHours = (BigDecimal) result[3];

            Map<String, Object> dayData = new HashMap<>();
            dayData.put("entryCount", entryCount);
            dayData.put("totalHours", totalHours);
            dayData.put("billableHours", billableHours);
            dayData.put("nonBillableHours", totalHours.subtract(billableHours));

            summary.put(date, dayData);
        }

        return summary;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<LocalDate, Map<String, Object>> getWeeklySummaryByUser(Long userId, LocalDate startDate, LocalDate endDate) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));

        List<Object[]> results = timesheetEntryRepository.getWeeklySummaryByUser(user, startDate, endDate);
        Map<LocalDate, Map<String, Object>> summary = new LinkedHashMap<>();

        for (Object[] result : results) {
            LocalDate weekStart = (LocalDate) result[0];
            Long entryCount = (Long) result[1];
            BigDecimal totalHours = (BigDecimal) result[2];
            Long daysWorked = (Long) result[3];

            Map<String, Object> weekData = new HashMap<>();
            weekData.put("entryCount", entryCount);
            weekData.put("totalHours", totalHours);
            weekData.put("daysWorked", daysWorked);
            weekData.put("averageHoursPerDay", daysWorked > 0 ? totalHours.divide(BigDecimal.valueOf(daysWorked), 2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);

            summary.put(weekStart, weekData);
        }

        return summary;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Map<String, Object>> getActivityTypeSummaryByUser(Long userId, LocalDate startDate, LocalDate endDate) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));

        List<Object[]> results = timesheetEntryRepository.getActivityTypeSummaryByUser(user, startDate, endDate);
        Map<String, Map<String, Object>> summary = new LinkedHashMap<>();

        for (Object[] result : results) {
            String activityType = (String) result[0];
            Long entryCount = (Long) result[1];
            BigDecimal totalHours = (BigDecimal) result[2];
            BigDecimal averageHours = (BigDecimal) result[3];

            Map<String, Object> activityData = new HashMap<>();
            activityData.put("entryCount", entryCount);
            activityData.put("totalHours", totalHours);
            activityData.put("averageHours", averageHours);

            summary.put(activityType, activityData);
        }

        return summary;
    }

    @Override
    public void updateWorkflowTotalHours(Long workflowId) {
        TimesheetApprovalWorkflow workflow = workflowRepository.findById(workflowId)
                .orElseThrow(() -> new RuntimeException("Workflow not found with id: " + workflowId));

        BigDecimal totalHours = timesheetEntryRepository.calculateTotalHoursByUserAndDateRange(
                workflow.getUser(), workflow.getWeekStartDate(), workflow.getWeekEndDate());

        workflow.setTotalHours(totalHours);
        workflowRepository.save(workflow);

        log.debug("Updated workflow {} total hours to: {}", workflowId, totalHours);
    }

    @Override
    @Transactional(readOnly = true)
    public List<String> getAvailableActivityTypes() {
        return Arrays.stream(TimesheetEntry.ActivityType.values())
                .map(Enum::name)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getValidationRules() {
        Map<String, Object> rules = new HashMap<>();
        rules.put("maxHoursPerDay", 24);
        rules.put("minHoursPerEntry", 0.1);
        rules.put("maxDescriptionLength", 1000);
        rules.put("canEditCurrentDayOnly", true);
        rules.put("availableActivityTypes", getAvailableActivityTypes());
        return rules;
    }

    // ===== Remaining Interface Methods (Stub Implementations) =====

    @Override
    @Transactional(readOnly = true)
    public Map<Long, Map<String, Object>> getIssueSummaryByUser(Long userId, LocalDate startDate, LocalDate endDate) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));

        List<Object[]> results = timesheetEntryRepository.getIssueSummaryByUser(user, startDate, endDate);
        Map<Long, Map<String, Object>> summary = new LinkedHashMap<>();

        for (Object[] result : results) {
            Issue issue = (Issue) result[0];
            Long entryCount = (Long) result[1];
            BigDecimal totalHours = (BigDecimal) result[2];

            Map<String, Object> issueData = new HashMap<>();
            issueData.put("issue", issue);
            issueData.put("entryCount", entryCount);
            issueData.put("totalHours", totalHours);

            summary.put(issue.getId(), issueData);
        }

        return summary;
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getTopUsersByTotalHours(LocalDate startDate, LocalDate endDate, int limit) {
        List<Object[]> results = timesheetEntryRepository.findTopUsersByTotalHours(startDate, endDate);
        List<Map<String, Object>> topUsers = new ArrayList<>();

        int count = 0;
        for (Object[] result : results) {
            if (count >= limit) break;

            User user = (User) result[0];
            BigDecimal totalHours = (BigDecimal) result[1];

            Map<String, Object> userData = new HashMap<>();
            userData.put("user", user);
            userData.put("totalHours", totalHours);

            topUsers.add(userData);
            count++;
        }

        return topUsers;
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getMostWorkedIssues(LocalDate startDate, LocalDate endDate, int limit) {
        List<Object[]> results = timesheetEntryRepository.findMostWorkedIssues(startDate, endDate);
        List<Map<String, Object>> mostWorkedIssues = new ArrayList<>();

        int count = 0;
        for (Object[] result : results) {
            if (count >= limit) break;

            Issue issue = (Issue) result[0];
            BigDecimal totalHours = (BigDecimal) result[1];
            Long userCount = (Long) result[2];

            Map<String, Object> issueData = new HashMap<>();
            issueData.put("issue", issue);
            issueData.put("totalHours", totalHours);
            issueData.put("userCount", userCount);

            mostWorkedIssues.add(issueData);
            count++;
        }

        return mostWorkedIssues;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getApprovalStatistics(LocalDate startDate, LocalDate endDate) {
        LocalDate cutoffDate = LocalDate.now().minusDays(7);
        Object[] stats = timesheetEntryRepository.getApprovalStatistics(startDate, endDate, cutoffDate);

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalEntries", stats[0]);
        statistics.put("approvedEntries", stats[1]);
        statistics.put("pendingEntries", stats[2]);
        statistics.put("overdueEntries", stats[3]);

        return statistics;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getWorkflowStatistics(LocalDate startDate, LocalDate endDate) {
        List<Object[]> results = workflowRepository.getWorkflowStatistics(startDate, endDate);
        Map<String, Object> statistics = new HashMap<>();

        for (Object[] result : results) {
            TimesheetApprovalWorkflow.WorkflowStatus status = (TimesheetApprovalWorkflow.WorkflowStatus) result[0];
            Long count = (Long) result[1];
            BigDecimal avgHours = (BigDecimal) result[2];
            BigDecimal totalHours = (BigDecimal) result[3];

            Map<String, Object> statusData = new HashMap<>();
            statusData.put("count", count);
            statusData.put("averageHours", avgHours);
            statusData.put("totalHours", totalHours);

            statistics.put(status.name(), statusData);
        }

        return statistics;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getUserDashboardData(Long userId, LocalDate startDate, LocalDate endDate) {
        Map<String, Object> dashboardData = new HashMap<>();

        // Basic statistics
        dashboardData.put("totalHours", getTotalHoursByUserAndDateRange(userId, startDate, endDate));
        dashboardData.put("billableHours", getTotalBillableHoursByUser(userId));
        dashboardData.put("dailySummary", getDailySummaryByUser(userId, startDate, endDate));
        dashboardData.put("activitySummary", getActivityTypeSummaryByUser(userId, startDate, endDate));
        dashboardData.put("issueSummary", getIssueSummaryByUser(userId, startDate, endDate));

        // Current week workflow
        Optional<TimesheetApprovalWorkflow> currentWorkflow = getCurrentWeekWorkflow(userId);
        dashboardData.put("currentWeekWorkflow", currentWorkflow.orElse(null));

        // Today's entries
        dashboardData.put("todaysEntries", getTodaysTimesheetEntries(userId));

        return dashboardData;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getAdminDashboardData(LocalDate startDate, LocalDate endDate) {
        Map<String, Object> dashboardData = new HashMap<>();

        dashboardData.put("approvalStatistics", getApprovalStatistics(startDate, endDate));
        dashboardData.put("workflowStatistics", getWorkflowStatistics(startDate, endDate));
        dashboardData.put("topUsers", getTopUsersByTotalHours(startDate, endDate, 10));
        dashboardData.put("mostWorkedIssues", getMostWorkedIssues(startDate, endDate, 10));

        return dashboardData;
    }

    @Override
    public void recalculateWorkflowTotals(Long userId) {
        List<TimesheetApprovalWorkflow> workflows = workflowRepository.findByUserIdOrderByWeekStartDateDesc(userId, Pageable.unpaged()).getContent();

        for (TimesheetApprovalWorkflow workflow : workflows) {
            updateWorkflowTotalHours(workflow.getId());
        }

        log.info("Recalculated workflow totals for user: {}", userId);
    }

    @Override
    public void cleanupOldTimesheetData(LocalDate cutoffDate) {
        timesheetEntryRepository.deleteByEntryDateBefore(cutoffDate);
        workflowRepository.deleteByWeekStartDateBefore(cutoffDate);
        log.info("Cleaned up timesheet data older than: {}", cutoffDate);
    }

    // ===== Stub implementations for export methods =====

    @Override
    public byte[] exportTimesheetDataToCsv(Long userId, LocalDate startDate, LocalDate endDate,
                                          List<Long> userIds, List<Long> issueIds) {
        // TODO: Implement CSV export
        throw new UnsupportedOperationException("CSV export not yet implemented");
    }

    @Override
    public byte[] exportTimesheetDataToExcel(Long userId, LocalDate startDate, LocalDate endDate,
                                            List<Long> userIds, List<Long> issueIds) {
        // TODO: Implement Excel export
        throw new UnsupportedOperationException("Excel export not yet implemented");
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getExportHistory(Long userId, Pageable pageable) {
        // TODO: Implement export history
        return Collections.emptyList();
    }
}
