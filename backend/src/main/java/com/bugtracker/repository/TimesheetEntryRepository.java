package com.bugtracker.repository;

import com.bugtracker.dto.timesheet.*;
import com.bugtracker.model.TimesheetEntry;
import com.bugtracker.model.User;
import com.bugtracker.model.Issue;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Timesheet Entry entities
 */
@Repository
public interface TimesheetEntryRepository extends JpaRepository<TimesheetEntry, Long> {

    /**
     * Find timesheet entries by user
     */
    Page<TimesheetEntry> findByUserOrderByEntryDateDescCreatedAtDesc(User user, Pageable pageable);

    /**
     * Find timesheet entries by user ID
     */
    Page<TimesheetEntry> findByUserIdOrderByEntryDateDescCreatedAtDesc(Long userId, Pageable pageable);

    /**
     * Find timesheet entries by user and date range
     */
    Page<TimesheetEntry> findByUserAndEntryDateBetweenOrderByEntryDateDescCreatedAtDesc(
            User user, LocalDate startDate, LocalDate endDate, Pageable pageable);

    /**
     * Find timesheet entries by user ID and date range
     */
    Page<TimesheetEntry> findByUserIdAndEntryDateBetweenOrderByEntryDateDescCreatedAtDesc(
            Long userId, LocalDate startDate, LocalDate endDate, Pageable pageable);

    /**
     * Find timesheet entries by issue
     */
    Page<TimesheetEntry> findByIssueOrderByEntryDateDescCreatedAtDesc(Issue issue, Pageable pageable);

    /**
     * Find timesheet entries by issue ID
     */
    Page<TimesheetEntry> findByIssueIdOrderByEntryDateDescCreatedAtDesc(Long issueId, Pageable pageable);

    /**
     * Find timesheet entries by date
     */
    Page<TimesheetEntry> findByEntryDateOrderByCreatedAtDesc(LocalDate entryDate, Pageable pageable);

    /**
     * Find timesheet entries by date range
     */
    Page<TimesheetEntry> findByEntryDateBetweenOrderByEntryDateDescCreatedAtDesc(
            LocalDate startDate, LocalDate endDate, Pageable pageable);

    /**
     * Find today's timesheet entries for a user
     */
    List<TimesheetEntry> findByUserAndEntryDateOrderByCreatedAtDesc(User user, LocalDate entryDate);

    /**
     * Find today's timesheet entries for a user by ID
     */
    List<TimesheetEntry> findByUserIdAndEntryDateOrderByCreatedAtDesc(Long userId, LocalDate entryDate);

    /**
     * Find pending approval entries
     */
    Page<TimesheetEntry> findByIsApprovedFalseOrderByEntryDateDescCreatedAtDesc(Pageable pageable);

    /**
     * Find pending approval entries for a specific user
     */
    Page<TimesheetEntry> findByUserAndIsApprovedFalseOrderByEntryDateDescCreatedAtDesc(User user, Pageable pageable);

    /**
     * Find entries by approval status
     */
    Page<TimesheetEntry> findByIsApprovedOrderByEntryDateDescCreatedAtDesc(Boolean isApproved, Pageable pageable);

    /**
     * Find entries by activity type
     */
    Page<TimesheetEntry> findByActivityTypeOrderByEntryDateDescCreatedAtDesc(String activityType, Pageable pageable);

    /**
     * Find billable entries
     */
    Page<TimesheetEntry> findByBillableOrderByEntryDateDescCreatedAtDesc(Boolean billable, Pageable pageable);

    /**
     * Check if entry exists for user, issue, date, and activity type
     */
    boolean existsByUserAndIssueAndEntryDateAndActivityType(User user, Issue issue, LocalDate entryDate, String activityType);

    /**
     * Find existing entry for user, issue, date, and activity type
     */
    Optional<TimesheetEntry> findByUserAndIssueAndEntryDateAndActivityType(User user, Issue issue, LocalDate entryDate, String activityType);

    /**
     * Calculate total hours by user
     */
    @Query("SELECT COALESCE(SUM(te.hoursSpent), 0) FROM TimesheetEntry te WHERE te.user = :user")
    BigDecimal calculateTotalHoursByUser(@Param("user") User user);

    /**
     * Calculate total hours by user and date range
     */
    @Query("SELECT COALESCE(SUM(te.hoursSpent), 0) FROM TimesheetEntry te WHERE te.user = :user AND te.entryDate BETWEEN :startDate AND :endDate")
    BigDecimal calculateTotalHoursByUserAndDateRange(@Param("user") User user, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Calculate total hours by issue
     */
    @Query("SELECT COALESCE(SUM(te.hoursSpent), 0) FROM TimesheetEntry te WHERE te.issue = :issue")
    BigDecimal calculateTotalHoursByIssue(@Param("issue") Issue issue);

    /**
     * Calculate total billable hours by user
     */
    @Query("SELECT COALESCE(SUM(te.hoursSpent), 0) FROM TimesheetEntry te WHERE te.user = :user AND te.billable = true")
    BigDecimal calculateTotalBillableHoursByUser(@Param("user") User user);

    /**
     * Calculate total hours by activity type
     */
    @Query("SELECT COALESCE(SUM(te.hoursSpent), 0) FROM TimesheetEntry te WHERE te.activityType = :activityType")
    BigDecimal calculateTotalHoursByActivityType(@Param("activityType") String activityType);

    /**
     * Find entries that need approval (older than current day and not approved)
     */
    @Query("SELECT te FROM TimesheetEntry te WHERE te.entryDate < CURRENT_DATE AND te.isApproved = false ORDER BY te.entryDate DESC")
    Page<TimesheetEntry> findEntriesNeedingApproval(Pageable pageable);

    /**
     * Find overdue entries (older than 7 days and not approved)
     */
    @Query("SELECT te FROM TimesheetEntry te WHERE te.entryDate < :cutoffDate AND te.isApproved = false ORDER BY te.entryDate DESC")
    Page<TimesheetEntry> findOverdueEntries(@Param("cutoffDate") LocalDate cutoffDate, Pageable pageable);

    /**
     * Get daily summary for user
     */
    @Query("SELECT te.entryDate, COUNT(te), SUM(te.hoursSpent), SUM(CASE WHEN te.billable = true THEN te.hoursSpent ELSE 0 END) " +
           "FROM TimesheetEntry te WHERE te.user = :user AND te.entryDate BETWEEN :startDate AND :endDate " +
           "GROUP BY te.entryDate ORDER BY te.entryDate DESC")
    List<Object[]> getDailySummaryByUser(@Param("user") User user, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Get weekly summary for user
     */
    @Query("SELECT FUNCTION('DATE_TRUNC', 'week', te.entryDate), COUNT(te), SUM(te.hoursSpent), COUNT(DISTINCT te.entryDate) " +
           "FROM TimesheetEntry te WHERE te.user = :user AND te.entryDate BETWEEN :startDate AND :endDate " +
           "GROUP BY FUNCTION('DATE_TRUNC', 'week', te.entryDate) ORDER BY FUNCTION('DATE_TRUNC', 'week', te.entryDate) DESC")
    List<Object[]> getWeeklySummaryByUser(@Param("user") User user, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Get activity type summary for user
     */
    @Query("SELECT new com.bugtracker.dto.timesheet.ActivityTypeSummaryDto(te.activityType, COUNT(te), SUM(te.hoursSpent), AVG(te.hoursSpent)) " +
           "FROM TimesheetEntry te WHERE te.user = :user AND te.entryDate BETWEEN :startDate AND :endDate " +
           "GROUP BY te.activityType ORDER BY SUM(te.hoursSpent) DESC")
    List<ActivityTypeSummaryDto> getActivityTypeSummaryByUser(@Param("user") User user, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Get issue summary for user
     */
    @Query("SELECT new com.bugtracker.dto.timesheet.IssueSummaryDto(te.issue, COUNT(te), SUM(te.hoursSpent)) " +
           "FROM TimesheetEntry te WHERE te.user = :user AND te.entryDate BETWEEN :startDate AND :endDate " +
           "GROUP BY te.issue ORDER BY SUM(te.hoursSpent) DESC")
    List<IssueSummaryDto> getIssueSummaryByUser(@Param("user") User user, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Find top users by total hours
     */
    @Query("SELECT new com.bugtracker.dto.timesheet.UserHoursSummaryDto(te.user, SUM(te.hoursSpent)) FROM TimesheetEntry te " +
           "WHERE te.entryDate BETWEEN :startDate AND :endDate " +
           "GROUP BY te.user ORDER BY SUM(te.hoursSpent) DESC")
    List<UserHoursSummaryDto> findTopUsersByTotalHours(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Find most worked issues
     */
    @Query("SELECT new com.bugtracker.dto.timesheet.IssueWorkSummaryDto(te.issue, SUM(te.hoursSpent), COUNT(DISTINCT te.user)) " +
           "FROM TimesheetEntry te WHERE te.entryDate BETWEEN :startDate AND :endDate " +
           "GROUP BY te.issue ORDER BY SUM(te.hoursSpent) DESC")
    List<IssueWorkSummaryDto> findMostWorkedIssues(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * Get approval statistics
     */
    @Query("SELECT new com.bugtracker.dto.timesheet.ApprovalStatisticsDto(" +
           "COUNT(te), " +
           "SUM(CASE WHEN te.isApproved = true THEN 1 ELSE 0 END), " +
           "SUM(CASE WHEN te.isApproved = false THEN 1 ELSE 0 END), " +
           "SUM(CASE WHEN te.isApproved = false AND te.entryDate < :cutoffDate THEN 1 ELSE 0 END)) " +
           "FROM TimesheetEntry te WHERE te.entryDate BETWEEN :startDate AND :endDate")
    ApprovalStatisticsDto getApprovalStatistics(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate, @Param("cutoffDate") LocalDate cutoffDate);

    /**
     * Delete entries older than specified date (for cleanup)
     */
    void deleteByEntryDateBefore(LocalDate cutoffDate);

    /**
     * Count entries by user for today
     */
    long countByUserAndEntryDate(User user, LocalDate entryDate);

    /**
     * Find entries by multiple users (for admin dashboard)
     */
    Page<TimesheetEntry> findByUserIdInOrderByEntryDateDescCreatedAtDesc(List<Long> userIds, Pageable pageable);

    /**
     * Find entries by multiple issues (for project reporting)
     */
    Page<TimesheetEntry> findByIssueIdInOrderByEntryDateDescCreatedAtDesc(List<Long> issueIds, Pageable pageable);
}
