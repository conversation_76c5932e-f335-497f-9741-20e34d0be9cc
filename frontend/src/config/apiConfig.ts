/**
 * API Configuration
 *
 * This file contains all API configuration including:
 * - Base URL
 * - API endpoints
 * - Feature flags for toggling between mock and real API
 */

// Base URL for API - Updated for HTTP local development
export const API_PORT = typeof import.meta !== 'undefined' && import.meta.env ? import.meta.env.VITE_API_PORT || '8080' : '8080';
export const API_HOST = typeof import.meta !== 'undefined' && import.meta.env ? import.meta.env.VITE_API_HOST || 'localhost' : 'localhost';
export const API_PREFIX = '/api';
export const API_BASE_URL = `http://${API_HOST}:${API_PORT}${API_PREFIX}`;

// Feature flag to toggle between mock and real API
export const USE_MOCK_API = false;

// API Endpoints
export const API_ENDPOINTS = {
  // Auth endpoints
  AUTH: {
    SIGNIN: '/auth/signin',
    SIGNUP: '/auth/signup',
    REFRESH_TOKEN: '/auth/refresh-token',
  },

  // User endpoints
  USERS: {
    BASE: '/users',
    CURRENT: '/users/me',
    BY_ID: (id: string | number) => `/users/${id}`,
  },

  // Role endpoints
  ROLES: {
    BASE: '/roles',
    ALL: '/roles/all',
    SEARCH: '/roles/search',
    BY_ID: (id: string | number) => `/roles/${id}`,
    ACTIVATE: (id: string | number) => `/roles/${id}/activate`,
    INITIALIZE: '/roles/initialize',
  },

  // Issue endpoints
  ISSUES: {
    BASE: '/issues',
    BY_ID: (id: string | number) => `/issues/${id}`,
    STATUS: (id: string | number) => `/issues/${id}/status`,
    RECENT: '/issues/recent',
    OVERDUE: '/issues/overdue',
    STATS: '/issues/stats/dashboard',
    REPORT: '/issues/report',
    BULK: '/issues/bulk',
    AI_ASSIST: '/issues/ai-assist',
    WATCHERS: {
      ADD: (issueId: string | number, userId: string | number) =>
        `/issues/${issueId}/watchers/${userId}`,
      REMOVE: (issueId: string | number, userId: string | number) =>
        `/issues/${issueId}/watchers/${userId}`,
    },
    ADD_LABEL: (issueId: string | number, labelId: string | number) =>
      `/issues/${issueId}/labels/${labelId}`,
    REMOVE_LABEL: (issueId: string | number, labelId: string | number) =>
      `/issues/${issueId}/labels/${labelId}`,
  },

  // Comment endpoints
  COMMENTS: {
    BASE: '/comments',
    BY_ID: (id: string | number) => `/comments/${id}`,
    BY_ISSUE: (issueId: string | number) => `/comments/issue/${issueId}`,
  },

  // Attachment endpoints
  ATTACHMENTS: {
    BASE: '/attachments',
    BY_ID: (id: string | number) => `/attachments/${id}`,
    BY_ISSUE: (issueId: string | number) => `/attachments/issue/${issueId}`,
  },

  // Lookup endpoints
  LOOKUPS: {
    BASE: '/lookups',
    BY_CATEGORY: (category: string) => `/lookups/${category}`,
  },

  // Issue Type Field Configuration endpoints
  ISSUE_TYPE_FIELDS: {
    BASE: '/issue-type-fields',
    CONFIGURATIONS: '/issue-type-fields/configurations',
    REQUIRED: (issueType: string) => `/issue-type-fields/${issueType}/required`,
    OPTIONAL: (issueType: string) => `/issue-type-fields/${issueType}/optional`,
    ALL: (issueType: string) => `/issue-type-fields/${issueType}/all`,
    DISPLAY_INFO: (issueType: string) => `/issue-type-fields/${issueType}/display-info`,
    VALIDATE: (issueType: string) => `/issue-type-fields/${issueType}/validate`,
  },

  // Label endpoints
  LABELS: {
    BASE: '/labels',
    BY_ID: (id: string | number) => `/labels/${id}`,
    SEARCH: '/labels/search',
  },

  // Module endpoints
  MODULES: {
    BASE: '/modules',
    BY_ID: (id: string | number) => `/modules/${id}`,
    WITH_SUBMODULES: '/modules/with-submodules',
    SUBMODULES: (moduleId: string | number) => `/modules/${moduleId}/submodules`,
    ALL_SUBMODULES: (moduleId: string | number) => `/modules/${moduleId}/submodules/all`,
    SUBMODULE_BY_ID: (id: string | number) => `/modules/submodules/${id}`,
    CREATE_SUBMODULE: (moduleId: string | number) => `/modules/${moduleId}/submodules`,
    UPDATE_SUBMODULE: (id: string | number) => `/modules/submodules/${id}`,
    DELETE_SUBMODULE: (id: string | number) => `/modules/submodules/${id}`,
    VALIDATE_SUBMODULE: (moduleId: string | number, submoduleId: string | number) =>
      `/modules/validate/${moduleId}/submodules/${submoduleId}`,
  },

  // Checklist endpoints
  CHECKLIST: {
    BASE: '/checklist-items',
    BY_ID: (id: string | number) => `/checklist-items/${id}`,
    BY_ISSUE: (issueId: string | number) => `/checklist-items/issue/${issueId}`,
    TOGGLE: (id: string | number) => `/checklist-items/${id}/toggle`,
    REORDER: '/checklist-items/reorder',
    STATS: (issueId: string | number) => `/checklist-items/issue/${issueId}/stats`,
  },

  // Notification endpoints
  NOTIFICATIONS: {
    BASE: '/notifications',
    UNREAD: '/notifications/unread',
    UNREAD_COUNT: '/notifications/unread/count',
    MARK_READ: (id: string | number) => `/notifications/${id}/read`,
    MARK_ALL_READ: '/notifications/read-all',
    BY_ID: (id: string | number) => `/notifications/${id}`,
  },

  // Tenant endpoints
  TENANTS: {
    BASE: '/tenants',
    REGISTER: '/tenants/register',
    BY_ID: (id: string | number) => `/tenants/${id}`,
    BY_TENANT_ID: (tenantId: string) => `/tenants/by-tenant-id/${tenantId}`,
    BY_SUBDOMAIN: (subdomain: string) => `/tenants/by-subdomain/${subdomain}`,
    SEARCH: '/tenants/search',
    SUSPEND: (id: string | number) => `/tenants/${id}/suspend`,
    ACTIVATE: (id: string | number) => `/tenants/${id}/activate`,
    DEACTIVATE: (id: string | number) => `/tenants/${id}/deactivate`,
    EXPIRED_SUBSCRIPTIONS: '/tenants/expired-subscriptions',
    EXPIRING_SOON: '/tenants/expiring-soon',
    USAGE: (tenantId: string) => `/tenants/${tenantId}/usage`,
    VALIDATE_TENANT_ID: '/tenants/validate-tenant-id',
    VALIDATE_SUBDOMAIN: '/tenants/validate-subdomain',
    VALIDATE_ADMIN_EMAIL: '/tenants/validate-admin-email',
  },

  // Tenant-aware auth endpoints
  TENANT_AUTH: {
    SIGNIN: '/tenant-auth/signin',
    SIGNUP: '/tenant-auth/signup',
    ME: '/tenant-auth/me',
    LOGOUT: '/tenant-auth/logout',
    VALIDATE: '/tenant-auth/validate',
    REFRESH: '/tenant-auth/refresh',
    TENANT_STATUS: '/tenant-auth/tenant-status',
  },

  // Timesheet endpoints
  TIMESHEET: {
    BASE: '/timesheet',
    ENTRIES: '/timesheet/entries',
    ENTRY_BY_ID: (id: string | number) => `/timesheet/entries/${id}`,
    ENTRIES_BY_USER: (userId: string | number) => `/timesheet/entries/user/${userId}`,
    ENTRIES_BY_USER_TODAY: (userId: string | number) => `/timesheet/entries/user/${userId}/today`,
    ENTRIES_BY_ISSUE: (issueId: string | number) => `/timesheet/entries/issue/${issueId}`,
    PENDING_APPROVAL: '/timesheet/entries/pending-approval',
    APPROVE_ENTRY: (id: string | number) => `/timesheet/entries/${id}/approve`,
    REJECT_ENTRY: (id: string | number) => `/timesheet/entries/${id}/reject`,
    BULK_APPROVE: '/timesheet/entries/bulk-approve',
    BULK_REJECT: '/timesheet/entries/bulk-reject',
    USER_DASHBOARD: (userId: string | number) => `/timesheet/dashboard/user/${userId}`,
    ADMIN_DASHBOARD: '/timesheet/dashboard/admin',
    VALIDATION_RULES: '/timesheet/validation-rules',
    WORKFLOWS: '/timesheet/workflows',
    WORKFLOW_BY_ID: (id: string | number) => `/timesheet/workflows/${id}`,
    SUBMIT_WORKFLOW: (id: string | number) => `/timesheet/workflows/${id}/submit`,
    APPROVE_WORKFLOW: (id: string | number) => `/timesheet/workflows/${id}/approve`,
    REJECT_WORKFLOW: (id: string | number) => `/timesheet/workflows/${id}/reject`,
  },

  // Health check
  HEALTH: '/actuator/health',
};

export default {
  API_PORT,
  API_HOST,
  API_PREFIX,
  API_BASE_URL,
  USE_MOCK_API,
  API_ENDPOINTS,
};
