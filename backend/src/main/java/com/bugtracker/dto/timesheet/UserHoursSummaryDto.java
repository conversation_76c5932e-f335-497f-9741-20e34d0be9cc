package com.bugtracker.dto.timesheet;

import com.bugtracker.model.User;
import java.math.BigDecimal;

/**
 * DTO for user hours summary data
 */
public class UserHoursSummaryDto {
    private User user;
    private BigDecimal totalHours;

    public UserHoursSummaryDto() {}

    public UserHoursSummaryDto(User user, BigDecimal totalHours) {
        this.user = user;
        this.totalHours = totalHours;
    }

    // Constructor for handling Double values from aggregate functions
    public UserHoursSummaryDto(User user, Double totalHours) {
        this.user = user;
        this.totalHours = totalHours != null ? BigDecimal.valueOf(totalHours) : BigDecimal.ZERO;
    }

    // Getters and setters
    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public BigDecimal getTotalHours() {
        return totalHours;
    }

    public void setTotalHours(BigDecimal totalHours) {
        this.totalHours = totalHours;
    }
}
