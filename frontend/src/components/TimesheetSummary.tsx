import React, { useEffect, useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  CircularProgress,
  Alert,
  Divider,
  Avatar,
  Tooltip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select
} from '@mui/material';
import {
  Schedule as ScheduleIcon,
  Add as AddIcon,
  Person as PersonIcon,
  AccessTime as AccessTimeIcon,
  Assignment as AssignmentIcon,
  TrendingUp as TrendingUpIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { useAppSelector, useAppDispatch } from '../store/hooks';
import timesheetService, { TimesheetEntry } from '../services/timesheetService';
import { formatDate, formatDateTime } from '../utils/dateUtils';
import authService from '../services/authService';
import tenantAuthService from '../services/tenantAuthService';
import unifiedAuthManager from '../services/unifiedAuthManager';
import tenantService from '../services/tenantService';

interface TimesheetSummaryProps {
  issueId: number;
  issueIdentifier: string;
  issueTitle: string;
}

interface SummaryStats {
  totalHours: number;
  currentUserHours: number;
  activityBreakdown: { [key: string]: number };
  userBreakdown: { [key: string]: { hours: number; name: string } };
  entryCount: number;
}

interface CreateEntryData {
  hoursSpent: string;
  description: string;
  activityType: string;
  billable: boolean;
  entryDate: string;
}

const ACTIVITY_TYPES = [
  { value: 'DEVELOPMENT', label: 'Development' },
  { value: 'TESTING', label: 'Testing' },
  { value: 'ANALYSIS', label: 'Analysis' },
  { value: 'DOCUMENTATION', label: 'Documentation' },
  { value: 'REVIEW', label: 'Code Review' },
  { value: 'MEETING', label: 'Meeting' },
  { value: 'RESEARCH', label: 'Research' },
  { value: 'DEPLOYMENT', label: 'Deployment' },
  { value: 'SUPPORT', label: 'Support' },
  { value: 'OTHER', label: 'Other' }
];

const TimesheetSummary: React.FC<TimesheetSummaryProps> = ({
  issueId,
  issueIdentifier,
  issueTitle
}) => {
  const dispatch = useAppDispatch();
  const { user: reduxUser } = useAppSelector((state) => state.auth);

  // Get user from multiple sources for better reliability
  const getCurrentUser = async () => {
    // Try Redux first
    if (reduxUser) {
      return reduxUser;
    }

    // Try unifiedAuthManager (best approach for tenant-aware auth)
    const unifiedUser = unifiedAuthManager.getUser();
    if (unifiedUser) {
      return unifiedUser;
    }

    // Try tenantAuthService
    const tenantUser = tenantAuthService.getCurrentUserFromStorage();
    if (tenantUser) {
      return tenantUser;
    }

    // Try to fetch current user from tenant-aware API
    try {
      const currentTenantId = tenantService.getCurrentTenantId() || localStorage.getItem('currentTenantId');
      if (currentTenantId) {
        const response = await fetch('/api/tenant-auth/me', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'X-Tenant-ID': currentTenantId
          }
        });

        if (response.ok) {
          const apiUser = await response.json();
          return apiUser;
        }
      }
    } catch (error) {
      console.warn('TimesheetSummary: Failed to fetch user from tenant API:', error);
    }

    // Fallback to regular authService
    const localUser = authService.getCurrentUser();
    if (localUser) {
      return localUser;
    }

    return null;
  };

  // Users can now log multiple entries for the same activity type on the same day
  // This allows tracking different work sessions throughout the day

  const [user, setUser] = useState<any>(null);
  const [entries, setEntries] = useState<TimesheetEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [summaryStats, setSummaryStats] = useState<SummaryStats>({
    totalHours: 0,
    currentUserHours: 0,
    activityBreakdown: {},
    userBreakdown: {},
    entryCount: 0
  });
  
  // Create entry dialog state
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [createLoading, setCreateLoading] = useState(false);
  const [createData, setCreateData] = useState<CreateEntryData>({
    hoursSpent: '',
    description: '',
    activityType: 'DEVELOPMENT',
    billable: true,
    entryDate: new Date().toISOString().split('T')[0] // Today's date
  });

  useEffect(() => {
    const loadUser = async () => {
      const currentUser = await getCurrentUser();
      setUser(currentUser);
    };

    loadUser();
    fetchTimesheetEntries();
  }, [issueId]);

  const fetchTimesheetEntries = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await timesheetService.getTimesheetEntriesByIssue(issueId);
      const entriesData = response.content || response;

      setEntries(entriesData);
      calculateSummaryStats(entriesData);
    } catch (err: any) {
      console.error('Error fetching timesheet entries:', err);
      setError(err.message || 'Failed to load timesheet data');
    } finally {
      setLoading(false);
    }
  };

  const calculateSummaryStats = (entriesData: TimesheetEntry[]) => {
    const stats: SummaryStats = {
      totalHours: 0,
      currentUserHours: 0,
      activityBreakdown: {},
      userBreakdown: {},
      entryCount: entriesData.length
    };

    entriesData.forEach(entry => {
      const hours = Number(entry.hoursSpent);
      
      // Total hours
      stats.totalHours += hours;
      
      // Current user hours
      if (user && entry.user.id === user.id) {
        stats.currentUserHours += hours;
      }
      
      // Activity breakdown
      const activityType = entry.activityTypeDisplay || entry.activityType;
      stats.activityBreakdown[activityType] = (stats.activityBreakdown[activityType] || 0) + hours;
      
      // User breakdown
      const userKey = entry.user.id.toString();
      const userName = `${entry.user.firstName} ${entry.user.lastName}`;
      if (!stats.userBreakdown[userKey]) {
        stats.userBreakdown[userKey] = { hours: 0, name: userName };
      }
      stats.userBreakdown[userKey].hours += hours;
    });

    setSummaryStats(stats);
  };

  const handleCreateEntry = async () => {
    // Clear any existing errors
    setError(null);

    const currentUser = await getCurrentUser();

    if (!currentUser) {
      console.error('TimesheetSummary: Cannot create timesheet entry - no authenticated user found');
      setError('Authentication error: Please log in again to create timesheet entries.');
      return;
    }

    // Validate form data before submission
    const hours = parseFloat(createData.hoursSpent);
    if (!createData.hoursSpent || isNaN(hours)) {
      setError('Please enter a valid number of hours');
      return;
    }

    if (hours <= 0) {
      setError('Hours spent must be greater than 0');
      return;
    }

    if (hours > 24) {
      setError('Hours spent cannot exceed 24 hours per day');
      return;
    }

    if (!createData.description.trim()) {
      setError('Please provide a description of the work done');
      return;
    }

    if (!createData.entryDate) {
      setError('Please select an entry date');
      return;
    }

    // Users can now log multiple entries for the same activity type

    try {
      setCreateLoading(true);

      const entryData = {
        userId: currentUser.id,
        issueId: issueId,
        entryDate: createData.entryDate,
        hoursSpent: hours,
        description: createData.description.trim(),
        activityType: createData.activityType,
        billable: createData.billable
      };

      await timesheetService.createTimesheetEntry(entryData);

      // Refresh the entries
      await fetchTimesheetEntries();

      // Close dialog and reset form
      setCreateDialogOpen(false);
      setCreateData({
        hoursSpent: '',
        description: '',
        activityType: 'DEVELOPMENT',
        billable: true,
        entryDate: new Date().toISOString().split('T')[0]
      });
    } catch (err: any) {
      console.error('Error creating timesheet entry:', err);
      let errorMessage = 'Failed to create timesheet entry';

      if (err.response?.data?.error) {
        errorMessage = err.response.data.error;
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
    } finally {
      setCreateLoading(false);
    }
  };

  const getActivityTypeColor = (activityType: string) => {
    const colors: { [key: string]: string } = {
      'DEVELOPMENT': '#2196F3',
      'TESTING': '#FF9800',
      'ANALYSIS': '#9C27B0',
      'DOCUMENTATION': '#4CAF50',
      'REVIEW': '#F44336',
      'MEETING': '#795548',
      'RESEARCH': '#607D8B',
      'DEPLOYMENT': '#E91E63',
      'SUPPORT': '#00BCD4',
      'OTHER': '#757575'
    };
    return colors[activityType] || colors['OTHER'];
  };

  const handleCreateDataChange = (field: string, value: any) => {
    setCreateData(prev => ({ ...prev, [field]: value }));

    // Clear error when user changes form data
    if (error) {
      setError(null);
    }

    // Real-time validation for hours field
    if (field === 'hoursSpent') {
      const hours = parseFloat(value);
      if (hours > 24) {
        setError('Hours spent cannot exceed 24 hours per day');
      } else if (hours <= 0) {
        setError('Hours spent must be greater than 0');
      }
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header with Create Button */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box display="flex" alignItems="center">
          <ScheduleIcon sx={{ mr: 1, color: 'text.secondary' }} />
          <Typography variant="h6" fontWeight="bold">
            Time Tracking Summary
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setCreateDialogOpen(true)}
          size="small"
        >
          Log Time
        </Button>
      </Box>

      {/* Summary Statistics Cards */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={6} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ textAlign: 'center', p: { xs: 1.5, sm: 2 } }}>
              <AccessTimeIcon sx={{ fontSize: { xs: 32, sm: 40 }, color: 'primary.main', mb: 1 }} />
              <Typography variant={{ xs: 'h5', sm: 'h4' }} fontWeight="bold" color="primary">
                {summaryStats.totalHours.toFixed(1)}h
              </Typography>
              <Typography variant="body2" color="text.secondary" fontSize={{ xs: '0.75rem', sm: '0.875rem' }}>
                Total Hours
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={6} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ textAlign: 'center', p: { xs: 1.5, sm: 2 } }}>
              <PersonIcon sx={{ fontSize: { xs: 32, sm: 40 }, color: 'success.main', mb: 1 }} />
              <Typography variant={{ xs: 'h5', sm: 'h4' }} fontWeight="bold" color="success.main">
                {summaryStats.currentUserHours.toFixed(1)}h
              </Typography>
              <Typography variant="body2" color="text.secondary" fontSize={{ xs: '0.75rem', sm: '0.875rem' }}>
                Your Hours
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={6} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ textAlign: 'center', p: { xs: 1.5, sm: 2 } }}>
              <AssignmentIcon sx={{ fontSize: { xs: 32, sm: 40 }, color: 'info.main', mb: 1 }} />
              <Typography variant={{ xs: 'h5', sm: 'h4' }} fontWeight="bold" color="info.main">
                {summaryStats.entryCount}
              </Typography>
              <Typography variant="body2" color="text.secondary" fontSize={{ xs: '0.75rem', sm: '0.875rem' }}>
                Total Entries
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={6} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ textAlign: 'center', p: { xs: 1.5, sm: 2 } }}>
              <TrendingUpIcon sx={{ fontSize: { xs: 32, sm: 40 }, color: 'warning.main', mb: 1 }} />
              <Typography variant={{ xs: 'h5', sm: 'h4' }} fontWeight="bold" color="warning.main">
                {Object.keys(summaryStats.activityBreakdown).length}
              </Typography>
              <Typography variant="body2" color="text.secondary" fontSize={{ xs: '0.75rem', sm: '0.875rem' }}>
                Activity Types
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Activity Breakdown */}
      {Object.keys(summaryStats.activityBreakdown).length > 0 && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" fontWeight="bold" mb={2}>
            Hours by Activity Type
          </Typography>
          <Grid container spacing={1}>
            {Object.entries(summaryStats.activityBreakdown).map(([activity, hours]) => (
              <Grid item key={activity}>
                <Chip
                  label={`${activity}: ${hours.toFixed(1)}h`}
                  sx={{
                    bgcolor: getActivityTypeColor(activity),
                    color: 'white',
                    fontWeight: 'medium'
                  }}
                />
              </Grid>
            ))}
          </Grid>
        </Paper>
      )}

      {/* User Breakdown */}
      {Object.keys(summaryStats.userBreakdown).length > 0 && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" fontWeight="bold" mb={2}>
            Hours by Team Member
          </Typography>
          <Grid container spacing={2}>
            {Object.entries(summaryStats.userBreakdown).map(([userId, userData]) => (
              <Grid item xs={12} sm={6} md={4} key={userId}>
                <Box
                  display="flex"
                  alignItems="center"
                  p={{ xs: 1.5, sm: 2 }}
                  bgcolor="grey.50"
                  borderRadius={1}
                  sx={{ minHeight: { xs: 60, sm: 70 } }}
                >
                  <Avatar sx={{ mr: 2, bgcolor: 'primary.main', width: { xs: 36, sm: 40 }, height: { xs: 36, sm: 40 } }}>
                    {userData.name.split(' ').map(n => n[0]).join('')}
                  </Avatar>
                  <Box>
                    <Typography variant="body2" fontWeight="medium" fontSize={{ xs: '0.875rem', sm: '1rem' }}>
                      {userData.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" fontSize={{ xs: '0.75rem', sm: '0.875rem' }}>
                      {userData.hours.toFixed(1)} hours
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            ))}
          </Grid>
        </Paper>
      )}

      {/* Daily Hours Breakdown Table */}
      {entries.length > 0 ? (
        <Paper>
          <Box p={2}>
            <Typography variant="h6" fontWeight="bold" mb={2}>
              Daily Hours Breakdown
            </Typography>
          </Box>
          <TableContainer sx={{ overflowX: 'auto' }}>
            <Table sx={{ minWidth: 650 }}>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ minWidth: 100 }}>Date</TableCell>
                  <TableCell sx={{ minWidth: 150 }}>User</TableCell>
                  <TableCell sx={{ minWidth: 80 }}>Hours</TableCell>
                  <TableCell sx={{ minWidth: 120 }}>Activity</TableCell>
                  <TableCell sx={{ minWidth: 200 }}>Description</TableCell>
                  <TableCell sx={{ minWidth: 100 }}>Status</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {entries.map((entry) => (
                  <TableRow key={entry.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {formatDate(entry.entryDate)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        <Avatar sx={{ mr: 1, width: 32, height: 32, fontSize: '0.875rem' }}>
                          {entry.user.firstName[0]}{entry.user.lastName[0]}
                        </Avatar>
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {entry.user.firstName} {entry.user.lastName}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {entry.user.username}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold" color="primary">
                        {Number(entry.hoursSpent).toFixed(1)}h
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={entry.activityTypeDisplay || entry.activityType}
                        size="small"
                        sx={{
                          bgcolor: getActivityTypeColor(entry.activityType),
                          color: 'white',
                          fontSize: '0.75rem'
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <Tooltip title={entry.description} arrow>
                        <Typography
                          variant="body2"
                          sx={{
                            maxWidth: 200,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}
                        >
                          {entry.description}
                        </Typography>
                      </Tooltip>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={entry.isApproved ? 'Approved' : 'Pending'}
                        size="small"
                        color={entry.isApproved ? 'success' : 'warning'}
                        variant="outlined"
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      ) : (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <ScheduleIcon sx={{ fontSize: 64, color: 'text.disabled', mb: 2 }} />
          <Typography variant="h6" color="text.secondary" mb={1}>
            No time entries found
          </Typography>
          <Typography variant="body2" color="text.secondary" mb={3}>
            Start tracking time on this issue to see detailed breakdown here.
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setCreateDialogOpen(true)}
          >
            Log Your First Entry
          </Button>
        </Paper>
      )}

      {/* Create Entry Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">Log Time Entry</Typography>
            <IconButton onClick={() => setCreateDialogOpen(false)} size="small">
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            {/* Error Display */}
            {error && (
              <Alert
                severity="error"
                sx={{ mb: 2 }}
                onClose={() => setError(null)}
                action={
                  <IconButton
                    aria-label="close"
                    color="inherit"
                    size="small"
                    onClick={() => setError(null)}
                  >
                    <CloseIcon fontSize="inherit" />
                  </IconButton>
                }
              >
                <Typography variant="body2">
                  {error}
                </Typography>
              </Alert>
            )}

            {/* Issue Info */}
            <Paper variant="outlined" sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>
              <Typography variant="body2" color="text.secondary" mb={1}>
                Logging time for:
              </Typography>
              <Typography variant="body1" fontWeight="medium">
                {issueIdentifier} - {issueTitle}
              </Typography>
            </Paper>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Hours Spent"
                  type="number"
                  value={createData.hoursSpent}
                  onChange={(e) => handleCreateDataChange('hoursSpent', e.target.value)}
                  inputProps={{ min: 0.1, max: 24, step: 0.1 }}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Entry Date"
                  type="date"
                  value={createData.entryDate}
                  onChange={(e) => handleCreateDataChange('entryDate', e.target.value)}
                  InputLabelProps={{ shrink: true }}
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth required>
                  <InputLabel>Activity Type</InputLabel>
                  <Select
                    value={createData.activityType}
                    onChange={(e) => handleCreateDataChange('activityType', e.target.value)}
                    label="Activity Type"
                  >
                    {ACTIVITY_TYPES.map((type) => (
                      <MenuItem key={type.value} value={type.value}>
                        {type.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  multiline
                  rows={3}
                  value={createData.description}
                  onChange={(e) => handleCreateDataChange('description', e.target.value)}
                  placeholder="Describe the work done..."
                  required
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={() => setCreateDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleCreateEntry}
            disabled={createLoading || !createData.hoursSpent || !createData.description}
            startIcon={createLoading ? <CircularProgress size={16} /> : <AddIcon />}
          >
            {createLoading ? 'Creating...' : 'Log Time'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TimesheetSummary;
