import React, { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  AppBar,
  Box,
  CssBaseline,
  Divider,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Typography,
  Badge,
  Menu,
  MenuItem,
  Avatar
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  BugReport as BugReportIcon,
  ViewKanban as KanbanIcon,
  CalendarMonth as CalendarIcon,
  Person as PersonIcon,
  Notifications as NotificationsIcon,
  Logout as LogoutIcon,
  Assessment as ReportIcon,
  People as PeopleIcon,
  Mic as MicIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { logout } from '../store/slices/authSlice';
import unifiedAuthManager from '../services/unifiedAuthManager';
import { fetchUnreadCount } from '../store/slices/notificationSlice';
import { ROUTES, isPathActive } from '../config/routes';

const drawerWidth = 240;

const Layout: React.FC = () => {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [notificationsAnchorEl, setNotificationsAnchorEl] = useState<null | HTMLElement>(null);
  const [profileAnchorEl, setProfileAnchorEl] = useState<null | HTMLElement>(null);

  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const { unreadCount } = useAppSelector((state) => state.notifications);

  // Helper function to check if user is admin
  const isUserAdmin = () => {
    if (!user?.roles) return false;

    // Handle both string roles and object roles
    return user.roles.some(role =>
      typeof role === 'string' ? role === 'ROLE_ADMIN' : role.name === 'ROLE_ADMIN'
    );
  };

  // Debug log to see user data
  console.log('Layout: Current user:', user);
  console.log('Layout: User roles:', user?.roles);
  console.log('Layout: Is admin:', isUserAdmin());

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleNotificationsOpen = (event: React.MouseEvent<HTMLElement>) => {
    setNotificationsAnchorEl(event.currentTarget);
    // Mark notifications as read
  };

  const handleNotificationsClose = () => {
    setNotificationsAnchorEl(null);
  };

  const handleProfileOpen = (event: React.MouseEvent<HTMLElement>) => {
    setProfileAnchorEl(event.currentTarget);
  };

  const handleProfileClose = () => {
    setProfileAnchorEl(null);
  };

  const handleLogout = async () => {
    console.log('Layout: Starting unified logout...');

    try {
      // Use unified auth manager for comprehensive logout
      await unifiedAuthManager.logout();

    } catch (error) {
      console.error('Layout: Error during logout:', error);

      // Force logout even if error occurs
      await unifiedAuthManager.logout();
    }
  };

  /**
   * Production-grade navigation handler with error handling
   * @param path - Target route path
   */
  const handleNavigate = (path: string) => {
    try {
      console.log(`Layout: Navigating to ${path}`);
      navigate(path);
      setMobileOpen(false);
    } catch (error) {
      console.error('Layout: Navigation error:', error);
      // Fallback to dashboard on navigation error
      navigate(ROUTES.APP.DASHBOARD);
      setMobileOpen(false);
    }
  };

  const drawer = (
    <div>
      <Toolbar sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        py: 2
      }}>
        <Typography
          variant="h5"
          noWrap
          component="div"
          sx={{
            fontWeight: 700,
            background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}
        >
          Bug Tracker
        </Typography>
      </Toolbar>
      <Divider sx={{ mx: 2 }} />
      <List sx={{ px: 1 }}>
        <ListItem disablePadding sx={{ mb: 1 }}>
          <ListItemButton
            onClick={() => handleNavigate(ROUTES.APP.DASHBOARD)}
            sx={{
              borderRadius: 2,
              '&:hover': {
                backgroundColor: 'rgba(33, 150, 243, 0.08)',
              },
              ...(isPathActive(location.pathname, ROUTES.APP.DASHBOARD) && {
                backgroundColor: 'rgba(33, 150, 243, 0.12)',
              })
            }}
          >
            <ListItemIcon>
              <DashboardIcon color={isPathActive(location.pathname, ROUTES.APP.DASHBOARD) ? 'primary' : 'inherit'} />
            </ListItemIcon>
            <ListItemText
              primary="Dashboard"
              primaryTypographyProps={{
                fontWeight: isPathActive(location.pathname, ROUTES.APP.DASHBOARD) ? 600 : 400
              }}
            />
          </ListItemButton>
        </ListItem>
        <ListItem disablePadding sx={{ mb: 1 }}>
          <ListItemButton
            onClick={() => handleNavigate(ROUTES.APP.ISSUES.ROOT)}
            sx={{
              borderRadius: 2,
              '&:hover': {
                backgroundColor: 'rgba(33, 150, 243, 0.08)',
              },
              ...(isPathActive(location.pathname, ROUTES.APP.ISSUES.ROOT) && {
                backgroundColor: 'rgba(33, 150, 243, 0.12)',
              })
            }}
          >
            <ListItemIcon>
              <BugReportIcon color={isPathActive(location.pathname, ROUTES.APP.ISSUES.ROOT) ? 'primary' : 'inherit'} />
            </ListItemIcon>
            <ListItemText
              primary="Issues"
              primaryTypographyProps={{
                fontWeight: isPathActive(location.pathname, ROUTES.APP.ISSUES.ROOT) ? 600 : 400
              }}
            />
          </ListItemButton>
        </ListItem>
        <ListItem disablePadding sx={{ mb: 1 }}>
          <ListItemButton
            onClick={() => handleNavigate(ROUTES.APP.ISSUES.BOARD)}
            sx={{
              borderRadius: 2,
              '&:hover': {
                backgroundColor: 'rgba(33, 150, 243, 0.08)',
              },
              ...(isPathActive(location.pathname, ROUTES.APP.ISSUES.BOARD, true) && {
                backgroundColor: 'rgba(33, 150, 243, 0.12)',
              })
            }}
          >
            <ListItemIcon>
              <KanbanIcon color={isPathActive(location.pathname, ROUTES.APP.ISSUES.BOARD, true) ? 'primary' : 'inherit'} />
            </ListItemIcon>
            <ListItemText
              primary="Board"
              primaryTypographyProps={{
                fontWeight: isPathActive(location.pathname, ROUTES.APP.ISSUES.BOARD, true) ? 600 : 400
              }}
            />
          </ListItemButton>
        </ListItem>
        <ListItem disablePadding sx={{ mb: 1 }}>
          <ListItemButton
            onClick={() => handleNavigate(ROUTES.APP.ISSUES.CALENDAR)}
            sx={{
              borderRadius: 2,
              '&:hover': {
                backgroundColor: 'rgba(33, 150, 243, 0.08)',
              },
              ...(isPathActive(location.pathname, ROUTES.APP.ISSUES.CALENDAR, true) && {
                backgroundColor: 'rgba(33, 150, 243, 0.12)',
              })
            }}
          >
            <ListItemIcon>
              <CalendarIcon color={isPathActive(location.pathname, ROUTES.APP.ISSUES.CALENDAR, true) ? 'primary' : 'inherit'} />
            </ListItemIcon>
            <ListItemText
              primary="Calendar"
              primaryTypographyProps={{
                fontWeight: isPathActive(location.pathname, ROUTES.APP.ISSUES.CALENDAR, true) ? 600 : 400
              }}
            />
          </ListItemButton>
        </ListItem>
        <ListItem disablePadding sx={{ mb: 1 }}>
          <ListItemButton
            onClick={() => handleNavigate(ROUTES.APP.VOICE_ASSISTANT)}
            sx={{
              borderRadius: 2,
              '&:hover': {
                backgroundColor: 'rgba(33, 150, 243, 0.08)',
              },
              ...(isPathActive(location.pathname, ROUTES.APP.VOICE_ASSISTANT, true) && {
                backgroundColor: 'rgba(33, 150, 243, 0.12)',
              })
            }}
          >
            <ListItemIcon>
              <MicIcon color={isPathActive(location.pathname, ROUTES.APP.VOICE_ASSISTANT, true) ? 'primary' : 'inherit'} />
            </ListItemIcon>
            <ListItemText
              primary="Voice Assistant"
              primaryTypographyProps={{
                fontWeight: isPathActive(location.pathname, ROUTES.APP.VOICE_ASSISTANT, true) ? 600 : 400
              }}
            />
          </ListItemButton>
        </ListItem>
        <ListItem disablePadding sx={{ mb: 1 }}>
          <ListItemButton
            onClick={() => handleNavigate(ROUTES.APP.TIMESHEET.ENTRY)}
            sx={{
              borderRadius: 2,
              '&:hover': {
                backgroundColor: 'rgba(33, 150, 243, 0.08)',
              },
              ...(isPathActive(location.pathname, ROUTES.APP.TIMESHEET.BASE, true) && {
                backgroundColor: 'rgba(33, 150, 243, 0.12)',
              })
            }}
          >
            <ListItemIcon>
              <ScheduleIcon color={isPathActive(location.pathname, ROUTES.APP.TIMESHEET.BASE, true) ? 'primary' : 'inherit'} />
            </ListItemIcon>
            <ListItemText
              primary="Timesheet"
              primaryTypographyProps={{
                fontWeight: isPathActive(location.pathname, ROUTES.APP.TIMESHEET.BASE, true) ? 600 : 400
              }}
            />
          </ListItemButton>
        </ListItem>
      </List>
      <Divider sx={{ mx: 2 }} />
      <List sx={{ px: 1 }}>
        <ListItem disablePadding sx={{ mb: 1 }}>
          <ListItemButton
            onClick={() => handleNavigate(ROUTES.APP.REPORTS)}
            sx={{
              borderRadius: 2,
              '&:hover': {
                backgroundColor: 'rgba(33, 150, 243, 0.08)',
              },
              ...(isPathActive(location.pathname, ROUTES.APP.REPORTS, true) && {
                backgroundColor: 'rgba(33, 150, 243, 0.12)',
              })
            }}
          >
            <ListItemIcon>
              <ReportIcon color={isPathActive(location.pathname, ROUTES.APP.REPORTS, true) ? 'primary' : 'inherit'} />
            </ListItemIcon>
            <ListItemText
              primary="Reports"
              primaryTypographyProps={{
                fontWeight: isPathActive(location.pathname, ROUTES.APP.REPORTS, true) ? 600 : 400
              }}
            />
          </ListItemButton>
        </ListItem>
        {isUserAdmin() && (
          <ListItem disablePadding sx={{ mb: 1 }}>
            <ListItemButton
              onClick={() => handleNavigate(ROUTES.APP.USERS.ROOT)}
              sx={{
                borderRadius: 2,
                '&:hover': {
                  backgroundColor: 'rgba(33, 150, 243, 0.08)',
                },
                ...(isPathActive(location.pathname, ROUTES.APP.USERS.ROOT) && {
                  backgroundColor: 'rgba(33, 150, 243, 0.12)',
                })
              }}
            >
              <ListItemIcon>
                <PeopleIcon color={isPathActive(location.pathname, ROUTES.APP.USERS.ROOT) ? 'primary' : 'inherit'} />
              </ListItemIcon>
              <ListItemText
                primary="User Management"
                primaryTypographyProps={{
                  fontWeight: isPathActive(location.pathname, ROUTES.APP.USERS.ROOT) ? 600 : 400
                }}
              />
            </ListItemButton>
          </ListItem>
        )}
        <ListItem disablePadding sx={{ mb: 1 }}>
          <ListItemButton
            onClick={() => handleNavigate(ROUTES.APP.PROFILE)}
            sx={{
              borderRadius: 2,
              '&:hover': {
                backgroundColor: 'rgba(33, 150, 243, 0.08)',
              },
              ...(isPathActive(location.pathname, ROUTES.APP.PROFILE, true) && {
                backgroundColor: 'rgba(33, 150, 243, 0.12)',
              })
            }}
          >
            <ListItemIcon>
              <PersonIcon color={isPathActive(location.pathname, ROUTES.APP.PROFILE, true) ? 'primary' : 'inherit'} />
            </ListItemIcon>
            <ListItemText
              primary="Profile"
              primaryTypographyProps={{
                fontWeight: isPathActive(location.pathname, ROUTES.APP.PROFILE, true) ? 600 : 400
              }}
            />
          </ListItemButton>
        </ListItem>
      </List>
    </div>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />
      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` },
          borderRadius: { xs: 0, sm: '0 0 12px 0' },
          backdropFilter: 'blur(10px)',
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{
              mr: 2,
              display: { sm: 'none' },
              backgroundColor: 'rgba(0, 0, 0, 0.04)',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.08)',
              }
            }}
          >
            <MenuIcon />
          </IconButton>
          <Typography
            variant="h6"
            noWrap
            component="div"
            sx={{
              flexGrow: 1,
              fontWeight: 600,
              background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}
          >
            Bug Tracking System
          </Typography>

          <IconButton
            onClick={handleNotificationsOpen}
            sx={{
              mx: 1,
              backgroundColor: 'rgba(0, 0, 0, 0.04)',
              transition: 'all 0.2s',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.08)',
                transform: 'translateY(-2px)'
              }
            }}
          >
            <Badge badgeContent={unreadCount} color="error">
              <NotificationsIcon color="primary" />
            </Badge>
          </IconButton>

          <IconButton
            onClick={handleProfileOpen}
            sx={{
              ml: 1,
              transition: 'all 0.2s',
              '&:hover': {
                transform: 'translateY(-2px)'
              }
            }}
          >
            <Avatar
              sx={{
                width: 40,
                height: 40,
                bgcolor: 'primary.main',
                boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)'
              }}
            >
              {user?.username?.charAt(0).toUpperCase()}
            </Avatar>
          </IconButton>

          <Menu
            anchorEl={notificationsAnchorEl}
            open={Boolean(notificationsAnchorEl)}
            onClose={handleNotificationsClose}
            PaperProps={{
              sx: {
                width: 320,
                maxHeight: 500,
                borderRadius: 3,
                boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
                mt: 1.5,
                overflow: 'visible',
                '&:before': {
                  content: '""',
                  display: 'block',
                  position: 'absolute',
                  top: 0,
                  right: 14,
                  width: 10,
                  height: 10,
                  bgcolor: 'background.paper',
                  transform: 'translateY(-50%) rotate(45deg)',
                  zIndex: 0,
                },
              }
            }}
            transformOrigin={{ horizontal: 'right', vertical: 'top' }}
            anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          >
            <MenuItem
              onClick={() => handleNavigate(ROUTES.APP.NOTIFICATIONS)}
              sx={{
                borderRadius: 2,
                mx: 1,
                my: 0.5,
                '&:hover': {
                  backgroundColor: 'rgba(33, 150, 243, 0.08)',
                }
              }}
            >
              <Typography variant="body1" sx={{ fontWeight: 500, color: 'primary.main' }}>
                View All Notifications
              </Typography>
            </MenuItem>
            <Divider sx={{ my: 1 }} />
            {/* Notification items would go here */}
            <Box sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                No new notifications
              </Typography>
            </Box>
          </Menu>

          <Menu
            anchorEl={profileAnchorEl}
            open={Boolean(profileAnchorEl)}
            onClose={handleProfileClose}
            PaperProps={{
              sx: {
                borderRadius: 3,
                boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
                mt: 1.5,
                minWidth: 200,
                overflow: 'visible',
                '&:before': {
                  content: '""',
                  display: 'block',
                  position: 'absolute',
                  top: 0,
                  right: 14,
                  width: 10,
                  height: 10,
                  bgcolor: 'background.paper',
                  transform: 'translateY(-50%) rotate(45deg)',
                  zIndex: 0,
                },
              }
            }}
            transformOrigin={{ horizontal: 'right', vertical: 'top' }}
            anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          >
            <Box sx={{ px: 2, py: 1.5 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                {user?.username}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {user?.email}
              </Typography>
            </Box>
            <Divider />
            <MenuItem
              onClick={() => { handleProfileClose(); handleNavigate(ROUTES.APP.PROFILE); }}
              sx={{
                borderRadius: 2,
                mx: 1,
                my: 0.5,
                '&:hover': {
                  backgroundColor: 'rgba(33, 150, 243, 0.08)',
                }
              }}
            >
              <ListItemIcon>
                <PersonIcon fontSize="small" color="primary" />
              </ListItemIcon>
              <Typography variant="body1">Profile</Typography>
            </MenuItem>
            <Divider sx={{ my: 1 }} />
            <MenuItem
              onClick={handleLogout}
              sx={{
                borderRadius: 2,
                mx: 1,
                my: 0.5,
                '&:hover': {
                  backgroundColor: 'rgba(244, 67, 54, 0.08)',
                }
              }}
            >
              <ListItemIcon>
                <LogoutIcon fontSize="small" color="error" />
              </ListItemIcon>
              <Typography variant="body1" color="error">Logout</Typography>
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>
      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile.
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              borderRight: 'none',
              boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
              background: 'linear-gradient(180deg, rgba(248,249,250,1) 0%, rgba(255,255,255,1) 100%)',
            },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              borderRight: 'none',
              boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
              background: 'linear-gradient(180deg, rgba(248,249,250,1) 0%, rgba(255,255,255,1) 100%)',
              borderRadius: '0 12px 12px 0',
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          backgroundColor: '#f8f9fa',
          minHeight: '100vh'
        }}
      >
        <Toolbar />
        <Outlet />
      </Box>
    </Box>
  );
};

export default Layout;
