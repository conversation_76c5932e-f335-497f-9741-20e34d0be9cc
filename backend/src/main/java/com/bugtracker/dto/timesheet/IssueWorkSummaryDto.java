package com.bugtracker.dto.timesheet;

import com.bugtracker.model.Issue;
import java.math.BigDecimal;

/**
 * DTO for issue work summary data (most worked issues)
 */
public class IssueWorkSummaryDto {
    private Issue issue;
    private BigDecimal totalHours;
    private Long userCount;

    public IssueWorkSummaryDto() {}

    public IssueWorkSummaryDto(Issue issue, BigDecimal totalHours, Long userCount) {
        this.issue = issue;
        this.totalHours = totalHours;
        this.userCount = userCount;
    }

    // Constructor for handling Double values from aggregate functions
    public IssueWorkSummaryDto(Issue issue, Double totalHours, Long userCount) {
        this.issue = issue;
        this.totalHours = totalHours != null ? BigDecimal.valueOf(totalHours) : BigDecimal.ZERO;
        this.userCount = userCount;
    }

    // Getters and setters
    public Issue getIssue() {
        return issue;
    }

    public void setIssue(Issue issue) {
        this.issue = issue;
    }

    public BigDecimal getTotalHours() {
        return totalHours;
    }

    public void setTotalHours(BigDecimal totalHours) {
        this.totalHours = totalHours;
    }

    public Long getUserCount() {
        return userCount;
    }

    public void setUserCount(Long userCount) {
        this.userCount = userCount;
    }
}
