import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import timesheetService, { TimesheetEntry, TimesheetEntryRequest } from '../services/timesheetService';
import issueService from '../services/issueService';
import { useAppSelector } from '../store/hooks';

interface Issue {
  id: number;
  identifier: string;
  title: string;
  type: string;
  status: string;
}

const TimesheetEntryPage: React.FC = () => {
  const currentUser = useAppSelector(state => state.auth.user);
  const [entries, setEntries] = useState<TimesheetEntry[]>([]);
  const [issues, setIssues] = useState<Issue[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [editingEntry, setEditingEntry] = useState<number | null>(null);
  const [validationRules, setValidationRules] = useState<any>(null);

  // Form state
  const [formData, setFormData] = useState({
    issueId: '',
    entryDate: new Date(),
    hoursSpent: '',
    description: '',
    activityType: 'DEVELOPMENT',
    billable: true,
  });

  useEffect(() => {
    fetchTodaysEntries();
    fetchIssues();
    fetchValidationRules();
  }, []);

  const fetchTodaysEntries = async () => {
    if (!currentUser?.id) return;
    
    try {
      setLoading(true);
      const data = await timesheetService.getTodaysTimesheetEntries(currentUser.id);
      setEntries(data);
    } catch (err: any) {
      setError('Failed to fetch today\'s timesheet entries');
      console.error('Error fetching entries:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchIssues = async () => {
    try {
      const response = await issueService.getIssues({ size: 100 });
      setIssues(response.content || []);
    } catch (err: any) {
      console.error('Error fetching issues:', err);
    }
  };

  const fetchValidationRules = async () => {
    try {
      const rules = await timesheetService.getValidationRules();
      setValidationRules(rules);
    } catch (err: any) {
      console.error('Error fetching validation rules:', err);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentUser?.id) return;

    try {
      setLoading(true);
      setError(null);

      const request: TimesheetEntryRequest = {
        userId: currentUser.id,
        issueId: parseInt(formData.issueId),
        entryDate: formData.entryDate.toISOString().split('T')[0],
        hoursSpent: parseFloat(formData.hoursSpent),
        description: formData.description,
        activityType: formData.activityType,
        billable: formData.billable,
      };

      if (editingEntry) {
        await timesheetService.updateTimesheetEntry(editingEntry, {
          hoursSpent: request.hoursSpent,
          description: request.description,
          activityType: request.activityType,
          billable: request.billable,
        });
        setSuccess('Timesheet entry updated successfully');
        setEditingEntry(null);
      } else {
        await timesheetService.createTimesheetEntry(request);
        setSuccess('Timesheet entry created successfully');
      }

      // Reset form
      setFormData({
        issueId: '',
        entryDate: new Date(),
        hoursSpent: '',
        description: '',
        activityType: 'DEVELOPMENT',
        billable: true,
      });

      // Refresh entries
      fetchTodaysEntries();
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to save timesheet entry');
      console.error('Error saving entry:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (entry: TimesheetEntry) => {
    setEditingEntry(entry.id);
    setFormData({
      issueId: entry.issue.id.toString(),
      entryDate: new Date(entry.entryDate),
      hoursSpent: entry.hoursSpent.toString(),
      description: entry.description,
      activityType: entry.activityType,
      billable: entry.billable,
    });
  };

  const handleCancelEdit = () => {
    setEditingEntry(null);
    setFormData({
      issueId: '',
      entryDate: new Date(),
      hoursSpent: '',
      description: '',
      activityType: 'DEVELOPMENT',
      billable: true,
    });
  };

  const handleDelete = async (entryId: number) => {
    if (!window.confirm('Are you sure you want to delete this timesheet entry?')) {
      return;
    }

    try {
      setLoading(true);
      await timesheetService.deleteTimesheetEntry(entryId);
      setSuccess('Timesheet entry deleted successfully');
      fetchTodaysEntries();
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to delete timesheet entry');
      console.error('Error deleting entry:', err);
    } finally {
      setLoading(false);
    }
  };

  const getTotalHours = () => {
    return entries.reduce((total, entry) => total + entry.hoursSpent, 0);
  };

  const getSelectedIssue = () => {
    return issues.find(issue => issue.id.toString() === formData.issueId);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          Timesheet Entry
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Entry Form */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                {editingEntry ? 'Edit Time Entry' : 'Add Time Entry'}
              </Typography>

              <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <FormControl fullWidth required>
                      <InputLabel>Issue</InputLabel>
                      <Select
                        value={formData.issueId}
                        label="Issue"
                        onChange={(e) => setFormData({ ...formData, issueId: e.target.value })}
                      >
                        {issues.map((issue) => (
                          <MenuItem key={issue.id} value={issue.id.toString()}>
                            {issue.identifier} - {issue.title}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <DatePicker
                      label="Date"
                      value={formData.entryDate}
                      onChange={(date) => setFormData({ ...formData, entryDate: date || new Date() })}
                      maxDate={new Date()}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          required: true,
                        },
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      required
                      label="Hours Spent"
                      type="number"
                      inputProps={{ 
                        min: 0.1, 
                        max: validationRules?.maxHoursPerDay || 24, 
                        step: 0.1 
                      }}
                      value={formData.hoursSpent}
                      onChange={(e) => setFormData({ ...formData, hoursSpent: e.target.value })}
                      helperText="Enter hours as decimal (e.g., 2.5 for 2 hours 30 minutes)"
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <FormControl fullWidth required>
                      <InputLabel>Activity Type</InputLabel>
                      <Select
                        value={formData.activityType}
                        label="Activity Type"
                        onChange={(e) => setFormData({ ...formData, activityType: e.target.value })}
                      >
                        {validationRules?.availableActivityTypes?.map((type: string) => (
                          <MenuItem key={type} value={type}>
                            {timesheetService.getActivityTypeDisplayName(type)}
                          </MenuItem>
                        )) || [
                          <MenuItem key="DEVELOPMENT" value="DEVELOPMENT">Development</MenuItem>,
                          <MenuItem key="TESTING" value="TESTING">Testing</MenuItem>,
                          <MenuItem key="CODE_REVIEW" value="CODE_REVIEW">Code Review</MenuItem>,
                          <MenuItem key="ANALYSIS" value="ANALYSIS">Analysis</MenuItem>,
                          <MenuItem key="DOCUMENTATION" value="DOCUMENTATION">Documentation</MenuItem>,
                          <MenuItem key="MEETING" value="MEETING">Meeting</MenuItem>,
                          <MenuItem key="OTHER" value="OTHER">Other</MenuItem>,
                        ]}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      required
                      multiline
                      rows={3}
                      label="Description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      inputProps={{ 
                        maxLength: validationRules?.maxDescriptionLength || 1000 
                      }}
                      helperText={`${formData.description.length}/${validationRules?.maxDescriptionLength || 1000} characters`}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={formData.billable}
                          onChange={(e) => setFormData({ ...formData, billable: e.target.checked })}
                        />
                      }
                      label="Billable"
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', gap: 2 }}>
                      <Button
                        type="submit"
                        variant="contained"
                        startIcon={editingEntry ? <SaveIcon /> : <AddIcon />}
                        disabled={loading}
                      >
                        {editingEntry ? 'Update Entry' : 'Add Entry'}
                      </Button>

                      {editingEntry && (
                        <Button
                          variant="outlined"
                          startIcon={<CancelIcon />}
                          onClick={handleCancelEdit}
                        >
                          Cancel
                        </Button>
                      )}

                      {loading && <CircularProgress size={24} />}
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </Paper>
          </Grid>

          {/* Today's Entries */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Today's Entries
                </Typography>
                <Chip 
                  label={`Total: ${timesheetService.formatHours(getTotalHours())}`}
                  color="primary"
                  variant="outlined"
                />
              </Box>

              {entries.length === 0 ? (
                <Typography color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                  No time entries for today
                </Typography>
              ) : (
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  {entries.map((entry) => (
                    <Card key={entry.id} variant="outlined">
                      <CardContent sx={{ pb: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                          <Box sx={{ flex: 1 }}>
                            <Typography variant="subtitle2" color="primary">
                              {entry.issue.identifier} - {entry.issue.title}
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                              {entry.description}
                            </Typography>
                            <Box sx={{ display: 'flex', gap: 1, mt: 1, flexWrap: 'wrap' }}>
                              <Chip 
                                size="small" 
                                label={timesheetService.formatHours(entry.hoursSpent)}
                                color="primary"
                              />
                              <Chip 
                                size="small" 
                                label={entry.activityTypeDisplay}
                                variant="outlined"
                              />
                              {entry.billable && (
                                <Chip 
                                  size="small" 
                                  label="Billable"
                                  color="success"
                                  variant="outlined"
                                />
                              )}
                              <Chip 
                                size="small" 
                                label={entry.approvalStatus}
                                color={entry.isApproved ? 'success' : 'warning'}
                                variant="outlined"
                              />
                            </Box>
                          </Box>
                          <Box sx={{ display: 'flex', gap: 0.5 }}>
                            <Tooltip title="Edit">
                              <IconButton 
                                size="small" 
                                onClick={() => handleEdit(entry)}
                                disabled={!entry.isEditable}
                              >
                                <EditIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Delete">
                              <IconButton 
                                size="small" 
                                onClick={() => handleDelete(entry.id)}
                                disabled={!entry.isEditable}
                                color="error"
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  ))}
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </LocalizationProvider>
  );
};

export default TimesheetEntryPage;
