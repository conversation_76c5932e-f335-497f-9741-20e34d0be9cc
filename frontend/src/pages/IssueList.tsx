import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Button,
  Chip,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  TextField,
  Typography,
  Tooltip
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import {
  DataGrid,
  GridColDef,
  GridRenderCellParams,
  GridColumnVisibilityModel,
  GridSortModel
} from '@mui/x-data-grid';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  Upload as UploadIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchIssues, type FetchIssuesParams } from '../store/slices/issueSlice';
import { fetchLookupValues } from '../store/slices/lookupSlice';
import { ROUTES } from '../config/routes';
import { formatDate } from '../utils/dateUtils';
import {
  getPriorityColor,
  getSeverityColor,
  getStatusColor,
  getEnvironmentColor,
  getPriorityChipColor,
  getSeverityChipColor,
  getContrastTextColor
} from '../utils/colorUtils';

const IssueList: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  const { issues, loading, pagination } = useAppSelector((state) => state.issues);
  const {
    issueType,
    issueStatus,
    issueEnvironment,
    issuePriority
  } = useAppSelector((state) => state.lookup);

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('ALL');
  const [typeFilter, setTypeFilter] = useState('ALL');
  const [priorityFilter, setpriorityFilter] = useState('ALL');
  const [environmentFilter, setEnvironmentFilter] = useState('ALL');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);

  // Pagination state
  const [paginationModel, setPaginationModel] = useState({
    page: 0,
    pageSize: 10,
  });

  // Column visibility state - Bug ID and Actions are always visible
  const [columnVisibilityModel, setColumnVisibilityModel] = useState<GridColumnVisibilityModel>({
    identifier: true, // Bug ID - always visible
    title: true,
    type: true,
    status: true,
    priority: true,
    severity: true,
    assignee: true,
    environment: true,
    createdAt: true,
    actions: true // Actions - always visible
  });



  const queryParams = new URLSearchParams(location.search);
  const filterParam = queryParams.get('filter');

  // Fetch lookup values on component mount
  useEffect(() => {
    dispatch(fetchLookupValues());
  }, [dispatch]);

  useEffect(() => {
    if (filterParam === 'overdue') {
      dispatch(fetchIssues({
        overdue: true,
        page: paginationModel.page,
        size: paginationModel.pageSize
      }));
    } else {
      const params: FetchIssuesParams = {
        search: searchTerm || undefined,
        status: statusFilter !== 'ALL' ? statusFilter : undefined,
        type: typeFilter !== 'ALL' ? typeFilter : undefined,
        priority: priorityFilter !== 'ALL' ? priorityFilter : undefined,
        environment: environmentFilter !== 'ALL' ? environmentFilter : undefined,
        startDate: startDate || undefined,
        endDate: endDate || undefined,
        page: paginationModel.page,
        size: paginationModel.pageSize,
        sort: 'createdAt,desc'
      };
      dispatch(fetchIssues(params));
    }
  }, [dispatch, searchTerm, statusFilter, typeFilter, priorityFilter, environmentFilter, startDate, endDate, filterParam, paginationModel]);

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    // Reset to first page when search changes
    setPaginationModel(prev => ({ ...prev, page: 0 }));
  };

  const handleStatusFilterChange = (event: any) => {
    setStatusFilter(event.target.value);
    // Reset to first page when filter changes
    setPaginationModel(prev => ({ ...prev, page: 0 }));
  };

  const handleTypeFilterChange = (event: any) => {
    setTypeFilter(event.target.value);
    // Reset to first page when filter changes
    setPaginationModel(prev => ({ ...prev, page: 0 }));
  };

  const handlePriorityFilterChange = (event: any) => {
    setpriorityFilter(event.target.value);
    // Reset to first page when filter changes
    setPaginationModel(prev => ({ ...prev, page: 0 }));
  };

  const handleEnvironmentFilterChange = (event: any) => {
    setEnvironmentFilter(event.target.value);
    // Reset to first page when filter changes
    setPaginationModel(prev => ({ ...prev, page: 0 }));
  };

  const handleCreateIssue = () => {
    navigate(ROUTES.APP.ISSUES.CREATE);
  };

  const handleClearDateFilters = () => {
    setStartDate(null);
    setEndDate(null);
    // Reset to first page when date filters are cleared
    setPaginationModel(prev => ({ ...prev, page: 0 }));
  };

  const validateDateRange = (start: Date | null, end: Date | null): string | null => {
    if (start && end && start > end) {
      return 'Start date cannot be after end date';
    }
    return null;
  };

  const columns: GridColDef[] = [
    {
      field: 'identifier',
      headerName: 'Bug ID',
      width: 100,
      minWidth: 80,
      hideable: false, // Prevent hiding this critical column
      renderCell: (params: GridRenderCellParams) => (
        <Box
          sx={{
            fontWeight: 'medium',
            color: 'primary.main'
          }}
        >
          {params.value}
        </Box>
      )
    },
    {
      field: 'title',
      headerName: 'Title',
      flex: 1,
      minWidth: 200,
      renderCell: (params: GridRenderCellParams) => (
        <Tooltip title={params.value} placement="top">
          <Box
            sx={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              width: '100%'
            }}
          >
            {params.value}
          </Box>
        </Tooltip>
      )
    },
    {
      field: 'type',
      headerName: 'Type',
      width: 120,
      minWidth: 100,
      renderCell: (params: GridRenderCellParams) => (
        <Chip label={params.value} size="small" />
      )
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 140,
      minWidth: 120,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={params.value.replace('_', ' ')}
          color={getStatusColor(params.value)}
          size="small"
        />
      )
    },
    {
      field: 'priority',
      headerName: 'Priority',
      width: 120,
      minWidth: 100,
      renderCell: (params: GridRenderCellParams) => {
        const priorityColor = getPriorityColor(params.value);
        const textColor = getContrastTextColor(priorityColor);
        return (
          <Chip
            label={params.value}
            size="small"
            sx={{
              backgroundColor: priorityColor,
              color: textColor,
              fontWeight: 'medium'
            }}
          />
        );
      }
    },
    {
      field: 'severity',
      headerName: 'Severity',
      width: 120,
      minWidth: 100,
      renderCell: (params: GridRenderCellParams) => {
        const severityColor = getSeverityColor(params.value);
        const textColor = getContrastTextColor(severityColor);
        return (
          <Chip
            label={params.value}
            size="small"
            sx={{
              backgroundColor: severityColor,
              color: textColor,
              fontWeight: 'medium'
            }}
          />
        );
      }
    },
    {
      field: 'assignee',
      headerName: 'Assignee',
      width: 150,
      minWidth: 120,
      valueGetter: (params) => params.row.assignee ? params.row.assignee.username : 'Unassigned',
      renderCell: (params: GridRenderCellParams) => (
        <Tooltip title={params.value} placement="top">
          <Box
            sx={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              width: '100%'
            }}
          >
            {params.value}
          </Box>
        </Tooltip>
      )
    },
    {
      field: 'environment',
      headerName: 'Environment',
      width: 130,
      minWidth: 110,
      renderCell: (params: GridRenderCellParams) => (
        <Chip
          label={params.value}
          color={getEnvironmentColor(params.value)}
          size="small"
        />
      )
    },
    {
      field: 'createdAt',
      headerName: 'Created Date',
      width: 130,
      minWidth: 110,
      renderCell: (params: GridRenderCellParams) => (
        <Tooltip title={formatDate(params.value, {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })} placement="top">
          <Box
            sx={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              width: '100%'
            }}
          >
            {formatDate(params.value, {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric'
            })}
          </Box>
        </Tooltip>
      )
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 100,
      minWidth: 80,
      sortable: false,
      filterable: false,
      hideable: false, // Prevent hiding this critical column
      renderCell: (params: GridRenderCellParams) => (
        <Box>
          <Tooltip title="View/Edit">
            <IconButton
              size="small"
              onClick={() => navigate(ROUTES.APP.ISSUES.DETAIL(params.row.id))}
            >
              <VisibilityIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      )
    }
  ];

  return (
    <Box>
      <Grid container spacing={2} alignItems="center" sx={{ mb: 3 }}>
        <Grid item xs>
          <Typography variant="h4">Issues</Typography>
        </Grid>
        <Grid item>
          <Button
            variant="outlined"
            startIcon={<UploadIcon />}
            onClick={() => navigate(ROUTES.APP.ISSUES.IMPORT)}
            sx={{ mr: 1 }}
          >
            Import Issues
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateIssue}
          >
            Create Issue
          </Button>
        </Grid>
      </Grid>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="Search"
              variant="outlined"
              value={searchTerm}
              onChange={handleSearch}
              InputProps={{
                endAdornment: <SearchIcon />
              }}
            />
          </Grid>
          <Grid item xs={12} md={8}>
            <Grid container spacing={2}>
              <Grid item xs={6} sm={3}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={statusFilter}
                    label="Status"
                    onChange={handleStatusFilterChange}
                  >
                    <MenuItem value="ALL">All</MenuItem>
                    {issueStatus?.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={6} sm={3}>
                <FormControl fullWidth>
                  <InputLabel>Type</InputLabel>
                  <Select
                    value={typeFilter}
                    label="Type"
                    onChange={handleTypeFilterChange}
                  >
                    <MenuItem value="ALL">All</MenuItem>
                    {issueType?.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={6} sm={3}>
                <FormControl fullWidth>
                  <InputLabel>Priority</InputLabel>
                  <Select
                    value={priorityFilter}
                    label="Priority"
                    onChange={handlePriorityFilterChange}
                  >
                    <MenuItem value="ALL">All</MenuItem>
                    {issuePriority?.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={6} sm={3}>
                <FormControl fullWidth>
                  <InputLabel>Environment</InputLabel>
                  <Select
                    value={environmentFilter}
                    label="Environment"
                    onChange={handleEnvironmentFilterChange}
                  >
                    <MenuItem value="ALL">All</MenuItem>
                    {issueEnvironment?.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Grid>
        </Grid>

        {/* Date Range Filter Section */}
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <Grid container spacing={2} alignItems="center" sx={{ mt: 2 }}>
            <Grid item xs={12} sm={6} md={3}>
              <DatePicker
                label="Start Date"
                value={startDate}
                onChange={(newValue) => {
                  setStartDate(newValue);
                  const error = validateDateRange(newValue, endDate);
                  if (error) {
                    console.warn(error);
                  }
                  // Reset to first page when date filter changes
                  setPaginationModel(prev => ({ ...prev, page: 0 }));
                }}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    error: !!validateDateRange(startDate, endDate),
                    helperText: validateDateRange(startDate, endDate),
                    InputProps: {
                      endAdornment: startDate && (
                        <IconButton
                          size="small"
                          onClick={() => {
                            setStartDate(null);
                            setPaginationModel(prev => ({ ...prev, page: 0 }));
                          }}
                          edge="end"
                          sx={{ mr: 1 }}
                        >
                          <ClearIcon fontSize="small" />
                        </IconButton>
                      )
                    }
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <DatePicker
                label="End Date"
                value={endDate}
                onChange={(newValue) => {
                  setEndDate(newValue);
                  const error = validateDateRange(startDate, newValue);
                  if (error) {
                    console.warn(error);
                  }
                  // Reset to first page when date filter changes
                  setPaginationModel(prev => ({ ...prev, page: 0 }));
                }}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    error: !!validateDateRange(startDate, endDate),
                    helperText: validateDateRange(startDate, endDate),
                    InputProps: {
                      endAdornment: endDate && (
                        <IconButton
                          size="small"
                          onClick={() => {
                            setEndDate(null);
                            setPaginationModel(prev => ({ ...prev, page: 0 }));
                          }}
                          edge="end"
                          sx={{ mr: 1 }}
                        >
                          <ClearIcon fontSize="small" />
                        </IconButton>
                      )
                    }
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Button
                variant="text"
                startIcon={<ClearIcon />}
                onClick={handleClearDateFilters}
                disabled={!startDate && !endDate}
                size="small"
                sx={{
                  minWidth: 'auto',
                  color: 'text.secondary',
                  '&:hover': {
                    backgroundColor: 'action.hover'
                  }
                }}
              >
                Reset Search
              </Button>
            </Grid>
          </Grid>
        </LocalizationProvider>
      </Paper>

      <Paper sx={{ width: '100%' }}>
        <DataGrid
          rows={issues}
          columns={columns}
          loading={loading}
          // Server-side pagination
          paginationMode="server"
          rowCount={pagination.totalElements}
          paginationModel={paginationModel}
          onPaginationModelChange={setPaginationModel}
          pageSizeOptions={[10, 25, 50]}
          columnVisibilityModel={columnVisibilityModel}
          onColumnVisibilityModelChange={(newModel) => {
            // Prevent hiding critical columns
            const updatedModel = {
              ...newModel,
              identifier: true, // Always keep Bug ID visible
              actions: true     // Always keep Actions visible
            };
            setColumnVisibilityModel(updatedModel);
          }}
          disableRowSelectionOnClick
          autoHeight
          sx={{
            minHeight: 400,
            '& .MuiDataGrid-main': {
              overflow: 'hidden'
            },
            '& .MuiDataGrid-virtualScroller': {
              overflow: 'auto'
            },
            '& .MuiDataGrid-columnHeaders': {
              backgroundColor: 'grey.50',
              borderBottom: '2px solid',
              borderBottomColor: 'grey.200'
            },
            '& .MuiDataGrid-cell': {
              borderBottom: '1px solid',
              borderBottomColor: 'grey.100'
            },
            // Responsive adjustments
            '@media (max-width: 768px)': {
              '& .MuiDataGrid-columnHeader': {
                fontSize: '0.875rem'
              },
              '& .MuiDataGrid-cell': {
                fontSize: '0.875rem',
                padding: '8px'
              }
            }
          }}
        />
      </Paper>
    </Box>
  );
};

export default IssueList;
