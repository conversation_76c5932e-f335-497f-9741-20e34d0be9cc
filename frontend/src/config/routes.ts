/**
 * Centralized Route Configuration
 *
 * This file defines all application routes in a type-safe manner
 * to ensure consistency across navigation components and prevent
 * routing errors in the multitenant architecture.
 */

export interface RouteConfig {
  path: string;
  label: string;
  icon?: string;
  requiresAuth?: boolean;
  requiresRoles?: string[];
  children?: RouteConfig[];
}

/**
 * Application route paths - all protected routes are under /app
 */
export const ROUTES = {
  // Public routes
  PUBLIC: {
    WELCOME: '/welcome',
    TENANT_SELECTION: '/tenant-selection',
    LOGIN: '/login',
    REGISTER_TENANT: '/register-tenant',
    TEST_AUTH: '/test-auth',
    TEST_LOOKUPS_PUBLIC: '/test-lookups-public',
  },

  // Protected routes (all under /app)
  APP: {
    ROOT: '/app',
    DASHBOARD: '/app',
    DASHBOARD_ALT: '/app/dashboard',

    // Issues routes
    ISSUES: {
      ROOT: '/app/issues',
      CREATE: '/app/issues/create',
      BOARD: '/app/issues/board',
      CALENDAR: '/app/issues/calendar',
      IMPORT: '/app/issues/import',
      DETAIL: (id: string | number) => `/app/issues/${id}`,
      EDIT: (id: string | number) => `/app/issues/${id}/edit`,
    },

    // Voice Assistant
    VOICE_ASSISTANT: '/app/voice-assistant',

    // User management
    PROFILE: '/app/profile',
    USERS: {
      BASE: '/app/users',
      ROOT: '/app/users',
      CREATE: '/app/users/create',
      EDIT: (id: string | number) => `/app/users/${id}/edit`,
    },

    // Reports
    REPORTS: '/app/reports',

    // Timesheet routes
    TIMESHEET: {
      BASE: '/app/timesheet',
      ENTRY: '/app/timesheet/entry',
      DASHBOARD: '/app/timesheet/dashboard',
      ADMIN: '/app/timesheet/admin',
      APPROVAL: '/app/timesheet/approval',
    },

    // Test routes
    TEST_FIXES: '/app/test-fixes',
    TEST_LOOKUPS: '/app/test-lookups',

    // Notifications (if implemented)
    NOTIFICATIONS: '/app/notifications',
  }
} as const;

/**
 * Navigation menu configuration
 */
export const NAVIGATION_MENU: RouteConfig[] = [
  {
    path: ROUTES.APP.DASHBOARD,
    label: 'Dashboard',
    icon: 'Dashboard',
    requiresAuth: true,
  },
  {
    path: ROUTES.APP.ISSUES.ROOT,
    label: 'Issues',
    icon: 'BugReport',
    requiresAuth: true,
    children: [
      {
        path: ROUTES.APP.ISSUES.ROOT,
        label: 'Issue List',
        requiresAuth: true,
      },
      {
        path: ROUTES.APP.ISSUES.CREATE,
        label: 'Create Issue',
        requiresAuth: true,
      },
      {
        path: ROUTES.APP.ISSUES.BOARD,
        label: 'Board',
        icon: 'ViewKanban',
        requiresAuth: true,
      },
      {
        path: ROUTES.APP.ISSUES.CALENDAR,
        label: 'Calendar',
        icon: 'CalendarMonth',
        requiresAuth: true,
      },
    ],
  },
  {
    path: ROUTES.APP.VOICE_ASSISTANT,
    label: 'Voice Assistant',
    icon: 'Mic',
    requiresAuth: true,
  },
  {
    path: ROUTES.APP.TIMESHEET.BASE,
    label: 'Timesheet',
    icon: 'Schedule',
    requiresAuth: true,
    children: [
      {
        path: ROUTES.APP.TIMESHEET.ENTRY,
        label: 'Time Entry',
        requiresAuth: true,
      },
      {
        path: ROUTES.APP.TIMESHEET.DASHBOARD,
        label: 'My Timesheet',
        requiresAuth: true,
      },
      {
        path: ROUTES.APP.TIMESHEET.ADMIN,
        label: 'Admin Dashboard',
        requiresAuth: true,
        requiresRoles: ['ROLE_ADMIN'],
      },
      {
        path: ROUTES.APP.TIMESHEET.APPROVAL,
        label: 'Approval Queue',
        requiresAuth: true,
        requiresRoles: ['ROLE_ADMIN'],
      },
    ],
  },
  {
    path: ROUTES.APP.REPORTS,
    label: 'Reports',
    icon: 'Assessment',
    requiresAuth: true,
  },
  {
    path: ROUTES.APP.USERS.ROOT,
    label: 'User Management',
    icon: 'People',
    requiresAuth: true,
    requiresRoles: ['ROLE_ADMIN'],
    children: [
      {
        path: ROUTES.APP.USERS.ROOT,
        label: 'User List',
        requiresAuth: true,
        requiresRoles: ['ROLE_ADMIN'],
      },
      {
        path: ROUTES.APP.USERS.CREATE,
        label: 'Create User',
        requiresAuth: true,
        requiresRoles: ['ROLE_ADMIN'],
      },
    ],
  },
  {
    path: ROUTES.APP.PROFILE,
    label: 'Profile',
    icon: 'Person',
    requiresAuth: true,
  },
];

/**
 * Helper function to check if a path is active
 * @param currentPath - Current location pathname
 * @param targetPath - Target path to check against
 * @param exact - Whether to match exactly or use startsWith
 */
export const isPathActive = (
  currentPath: string,
  targetPath: string,
  exact: boolean = false
): boolean => {
  if (exact) {
    return currentPath === targetPath;
  }

  // Special handling for dashboard routes
  if (targetPath === ROUTES.APP.DASHBOARD) {
    return currentPath === ROUTES.APP.ROOT || currentPath === ROUTES.APP.DASHBOARD_ALT;
  }

  // For other routes, check if current path starts with target path
  return currentPath.startsWith(targetPath);
};

/**
 * Helper function to get the correct redirect URL after login
 * @param intendedPath - The path user was trying to access
 */
export const getLoginRedirectUrl = (intendedPath?: string): string => {
  if (!intendedPath || intendedPath === '/') {
    return ROUTES.APP.DASHBOARD;
  }

  // If the intended path is already under /app, use it
  if (intendedPath.startsWith('/app')) {
    return intendedPath;
  }

  // Otherwise, default to dashboard
  return ROUTES.APP.DASHBOARD;
};

/**
 * Helper function to validate if a route exists
 * @param path - Path to validate
 */
export const isValidRoute = (path: string): boolean => {
  const allRoutes = [
    ...Object.values(ROUTES.PUBLIC),
    ROUTES.APP.ROOT,
    ROUTES.APP.DASHBOARD_ALT,
    ROUTES.APP.ISSUES.ROOT,
    ROUTES.APP.ISSUES.CREATE,
    ROUTES.APP.ISSUES.BOARD,
    ROUTES.APP.ISSUES.CALENDAR,
    ROUTES.APP.ISSUES.IMPORT,
    ROUTES.APP.VOICE_ASSISTANT,
    ROUTES.APP.PROFILE,
    ROUTES.APP.USERS.ROOT,
    ROUTES.APP.USERS.CREATE,
    ROUTES.APP.REPORTS,
    ROUTES.APP.TIMESHEET.BASE,
    ROUTES.APP.TIMESHEET.ENTRY,
    ROUTES.APP.TIMESHEET.DASHBOARD,
    ROUTES.APP.TIMESHEET.ADMIN,
    ROUTES.APP.TIMESHEET.APPROVAL,
    ROUTES.APP.TEST_FIXES,
    ROUTES.APP.TEST_LOOKUPS,
    ROUTES.APP.NOTIFICATIONS,
  ];

  return allRoutes.includes(path) ||
         path.match(/^\/app\/issues\/\d+$/) || // Issue detail
         path.match(/^\/app\/issues\/\d+\/edit$/) || // Issue edit
         path.match(/^\/app\/users\/\d+\/edit$/); // User edit
};
