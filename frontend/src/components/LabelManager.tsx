import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Autocomplete,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  Close as CloseIcon,
  Label as LabelIcon
} from '@mui/icons-material';
import labelService, { Label } from '../services/labelService';

interface LabelManagerProps {
  issueId: number;
  currentLabels: Label[];
  onLabelsChange: (labels: Label[]) => void;
}

const LabelManager: React.FC<LabelManagerProps> = ({
  issueId,
  currentLabels,
  onLabelsChange
}) => {
  const [open, setOpen] = useState(false);
  const [allLabels, setAllLabels] = useState<Label[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedLabe<PERSON>, setSelectedLabels] = useState<Label[]>(currentLabels);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newLabelName, setNewLabelName] = useState('');
  const [newLabelColor, setNewLabelColor] = useState('#1976d2');
  const [newLabelDescription, setNewLabelDescription] = useState('');
  const [creating, setCreating] = useState(false);

  useEffect(() => {
    setSelectedLabels(currentLabels);
  }, [currentLabels]);

  const fetchAllLabels = async () => {
    try {
      setLoading(true);
      const labels = await labelService.getAllLabels();
      setAllLabels(labels);
    } catch (error: any) {
      console.error('Error fetching labels:', error);
      setError('Failed to fetch labels');
    } finally {
      setLoading(false);
    }
  };

  const handleOpen = () => {
    setOpen(true);
    fetchAllLabels();
  };

  const handleClose = () => {
    setOpen(false);
    setError(null);
    setShowCreateForm(false);
    setNewLabelName('');
    setNewLabelColor('#1976d2');
    setNewLabelDescription('');
  };

  const handleLabelToggle = async (label: Label) => {
    try {
      const isCurrentlySelected = selectedLabels.some(l => l.id === label.id);
      
      if (isCurrentlySelected) {
        // Remove label
        await labelService.removeLabelFromIssue(issueId, label.id);
        const newLabels = selectedLabels.filter(l => l.id !== label.id);
        setSelectedLabels(newLabels);
        onLabelsChange(newLabels);
      } else {
        // Add label
        await labelService.addLabelToIssue(issueId, label.id);
        const newLabels = [...selectedLabels, label];
        setSelectedLabels(newLabels);
        onLabelsChange(newLabels);
      }
    } catch (error: any) {
      console.error('Error toggling label:', error);
      setError('Failed to update label');
    }
  };

  const handleCreateLabel = async () => {
    if (!newLabelName.trim()) return;

    try {
      setCreating(true);
      const newLabel = await labelService.createLabel({
        name: newLabelName,
        color: newLabelColor,
        description: newLabelDescription
      });

      // Add to all labels list
      setAllLabels([...allLabels, newLabel]);

      // Reset form
      setNewLabelName('');
      setNewLabelColor('#1976d2');
      setNewLabelDescription('');
      setShowCreateForm(false);
    } catch (error: any) {
      console.error('Error creating label:', error);
      setError('Failed to create label. You may not have permission to create labels.');
    } finally {
      setCreating(false);
    }
  };

  const handleSave = () => {
    handleClose();
  };

  return (
    <>
      <Box sx={{ mb: 1 }}>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
          <LabelIcon fontSize="small" sx={{ mr: 1 }} />
          Labels
        </Typography>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap', minHeight: '32px' }}>
          {currentLabels.length > 0 ? (
            currentLabels.map((label) => (
              <Chip
                key={label.id}
                label={label.name}
                size="small"
                sx={{
                  backgroundColor: label.color,
                  color: '#fff',
                  fontWeight: 'medium',
                  '& .MuiChip-deleteIcon': {
                    color: '#fff'
                  }
                }}
                onDelete={() => handleLabelToggle(label)}
              />
            ))
          ) : (
            <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
              No labels assigned
            </Typography>
          )}

          <Button
            size="small"
            variant="outlined"
            startIcon={<AddIcon />}
            onClick={handleOpen}
            sx={{
              borderRadius: 2,
              textTransform: 'none',
              fontSize: '0.75rem',
              py: 0.5,
              px: 1
            }}
          >
            Add Label
          </Button>
        </Box>
      </Box>

      <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">Manage Labels</Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Box sx={{ mt: 1 }}>
              <Typography variant="subtitle2" sx={{ mb: 2 }}>
                Click labels to add or remove them:
              </Typography>
              
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {allLabels.map((label) => {
                  const isSelected = selectedLabels.some(l => l.id === label.id);
                  return (
                    <Chip
                      key={label.id}
                      label={label.name}
                      clickable
                      variant={isSelected ? 'filled' : 'outlined'}
                      onClick={() => handleLabelToggle(label)}
                      sx={{
                        backgroundColor: isSelected ? label.color : 'transparent',
                        borderColor: label.color,
                        color: isSelected ? '#fff' : label.color,
                        fontWeight: 'medium',
                        '&:hover': {
                          backgroundColor: isSelected ? label.color : `${label.color}20`
                        }
                      }}
                    />
                  );
                })}
              </Box>
              
              {allLabels.length === 0 && !loading && !showCreateForm && (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <LabelIcon sx={{ fontSize: 48, color: 'text.disabled', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
                    No Labels Available
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Labels help organize and categorize issues. Create your first label to get started.
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => setShowCreateForm(true)}
                    sx={{ mt: 1 }}
                  >
                    Create First Label
                  </Button>
                </Box>
              )}

              {/* Create Label Form */}
              {showCreateForm && (
                <Box sx={{ mt: 2, p: 2, bgcolor: 'rgba(0,0,0,0.02)', borderRadius: 2 }}>
                  <Typography variant="subtitle2" sx={{ mb: 2 }}>
                    Create New Label
                  </Typography>
                  <TextField
                    fullWidth
                    size="small"
                    label="Label Name"
                    value={newLabelName}
                    onChange={(e) => setNewLabelName(e.target.value)}
                    sx={{ mb: 2 }}
                  />
                  <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                    <TextField
                      type="color"
                      label="Color"
                      value={newLabelColor}
                      onChange={(e) => setNewLabelColor(e.target.value)}
                      sx={{ width: 100 }}
                      size="small"
                    />
                    <TextField
                      fullWidth
                      size="small"
                      label="Description (optional)"
                      value={newLabelDescription}
                      onChange={(e) => setNewLabelDescription(e.target.value)}
                    />
                  </Box>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      variant="contained"
                      size="small"
                      onClick={handleCreateLabel}
                      disabled={!newLabelName.trim() || creating}
                      startIcon={creating ? <CircularProgress size={16} /> : <AddIcon />}
                    >
                      {creating ? 'Creating...' : 'Create Label'}
                    </Button>
                    <Button
                      size="small"
                      onClick={() => setShowCreateForm(false)}
                    >
                      Cancel
                    </Button>
                  </Box>
                </Box>
              )}

              {/* Show create button when labels exist */}
              {allLabels.length > 0 && !showCreateForm && (
                <Box sx={{ mt: 2, textAlign: 'center' }}>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<AddIcon />}
                    onClick={() => setShowCreateForm(true)}
                  >
                    Create New Label
                  </Button>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleClose}>Close</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default LabelManager;
