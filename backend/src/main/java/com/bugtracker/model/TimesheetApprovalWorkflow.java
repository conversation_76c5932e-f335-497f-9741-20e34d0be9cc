package com.bugtracker.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Timesheet Approval Workflow entity for managing weekly timesheet approvals
 */
@Entity
@Table(name = "timesheet_approval_workflow",
       uniqueConstraints = @UniqueConstraint(columnNames = {"user_id", "week_start_date"}))
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class TimesheetApprovalWorkflow {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "user_id", nullable = false)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "roles", "password"})
    private User user;

    @Column(name = "week_start_date", nullable = false)
    private LocalDate weekStartDate;

    @Column(name = "week_end_date", nullable = false)
    private LocalDate weekEndDate;

    @Column(name = "total_hours", nullable = false, precision = 6, scale = 2)
    @Builder.Default
    private BigDecimal totalHours = BigDecimal.ZERO;

    @Column(nullable = false, length = 20)
    @Enumerated(EnumType.STRING)
    @Builder.Default
    private WorkflowStatus status = WorkflowStatus.DRAFT;

    @Column(name = "submitted_at")
    private LocalDateTime submittedAt;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "submitted_by")
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "roles", "password"})
    private User submittedBy;

    @Column(name = "reviewed_at")
    private LocalDateTime reviewedAt;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "reviewed_by")
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "roles", "password"})
    private User reviewedBy;

    @Column(name = "review_notes", columnDefinition = "TEXT")
    private String reviewNotes;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * Workflow status enum
     */
    public enum WorkflowStatus {
        DRAFT("Draft"),
        SUBMITTED("Submitted"),
        APPROVED("Approved"),
        REJECTED("Rejected");

        private final String displayName;

        WorkflowStatus(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }

        @Override
        public String toString() {
            return this.name();
        }
    }

    /**
     * Submit timesheet for approval
     */
    public void submit(User submitter) {
        if (this.status != WorkflowStatus.DRAFT) {
            throw new IllegalStateException("Only draft timesheets can be submitted");
        }
        this.status = WorkflowStatus.SUBMITTED;
        this.submittedAt = LocalDateTime.now();
        this.submittedBy = submitter;
    }

    /**
     * Approve timesheet
     */
    public void approve(User reviewer, String notes) {
        if (this.status != WorkflowStatus.SUBMITTED) {
            throw new IllegalStateException("Only submitted timesheets can be approved");
        }
        this.status = WorkflowStatus.APPROVED;
        this.reviewedAt = LocalDateTime.now();
        this.reviewedBy = reviewer;
        this.reviewNotes = notes;
    }

    /**
     * Reject timesheet
     */
    public void reject(User reviewer, String notes) {
        if (this.status != WorkflowStatus.SUBMITTED) {
            throw new IllegalStateException("Only submitted timesheets can be rejected");
        }
        this.status = WorkflowStatus.REJECTED;
        this.reviewedAt = LocalDateTime.now();
        this.reviewedBy = reviewer;
        this.reviewNotes = notes;
    }

    /**
     * Reset to draft status
     */
    public void resetToDraft() {
        this.status = WorkflowStatus.DRAFT;
        this.submittedAt = null;
        this.submittedBy = null;
        this.reviewedAt = null;
        this.reviewedBy = null;
        this.reviewNotes = null;
    }

    /**
     * Check if timesheet can be edited
     */
    public boolean isEditable() {
        return status == WorkflowStatus.DRAFT || status == WorkflowStatus.REJECTED;
    }

    /**
     * Check if timesheet can be submitted
     */
    public boolean canBeSubmitted() {
        return status == WorkflowStatus.DRAFT && totalHours.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * Check if timesheet can be approved/rejected
     */
    public boolean canBeReviewed() {
        return status == WorkflowStatus.SUBMITTED;
    }

    /**
     * Check if timesheet is overdue for review
     */
    public boolean isOverdueForReview() {
        return status == WorkflowStatus.SUBMITTED && 
               submittedAt != null && 
               submittedAt.isBefore(LocalDateTime.now().minusDays(3));
    }

    /**
     * Get total hours as double
     */
    public Double getTotalHoursAsDouble() {
        return totalHours != null ? totalHours.doubleValue() : 0.0;
    }

    /**
     * Set total hours from double
     */
    public void setTotalHoursFromDouble(Double hours) {
        this.totalHours = hours != null ? BigDecimal.valueOf(hours) : BigDecimal.ZERO;
    }

    /**
     * Get status display name
     */
    @JsonProperty("statusDisplay")
    public String getStatusDisplay() {
        return status.getDisplayName();
    }

    /**
     * Get week description
     */
    @JsonProperty("weekDescription")
    public String getWeekDescription() {
        return String.format("Week of %s to %s", weekStartDate, weekEndDate);
    }

    /**
     * Check if this is current week
     */
    @JsonProperty("isCurrentWeek")
    public boolean isCurrentWeek() {
        LocalDate now = LocalDate.now();
        return !now.isBefore(weekStartDate) && !now.isAfter(weekEndDate);
    }

    /**
     * Get days since submission
     */
    @JsonProperty("daysSinceSubmission")
    public Long getDaysSinceSubmission() {
        if (submittedAt == null) {
            return null;
        }
        return java.time.temporal.ChronoUnit.DAYS.between(submittedAt.toLocalDate(), LocalDate.now());
    }

    /**
     * Get days since review
     */
    @JsonProperty("daysSinceReview")
    public Long getDaysSinceReview() {
        if (reviewedAt == null) {
            return null;
        }
        return java.time.temporal.ChronoUnit.DAYS.between(reviewedAt.toLocalDate(), LocalDate.now());
    }

    /**
     * Calculate week start date from any date
     */
    public static LocalDate getWeekStartDate(LocalDate date) {
        return date.minusDays(date.getDayOfWeek().getValue() - 1);
    }

    /**
     * Calculate week end date from any date
     */
    public static LocalDate getWeekEndDate(LocalDate date) {
        return getWeekStartDate(date).plusDays(6);
    }

    /**
     * Create workflow for current week
     */
    public static TimesheetApprovalWorkflow createForCurrentWeek(User user) {
        LocalDate now = LocalDate.now();
        return TimesheetApprovalWorkflow.builder()
                .user(user)
                .weekStartDate(getWeekStartDate(now))
                .weekEndDate(getWeekEndDate(now))
                .totalHours(BigDecimal.ZERO)
                .status(WorkflowStatus.DRAFT)
                .build();
    }

    /**
     * Create workflow for specific week
     */
    public static TimesheetApprovalWorkflow createForWeek(User user, LocalDate weekDate) {
        return TimesheetApprovalWorkflow.builder()
                .user(user)
                .weekStartDate(getWeekStartDate(weekDate))
                .weekEndDate(getWeekEndDate(weekDate))
                .totalHours(BigDecimal.ZERO)
                .status(WorkflowStatus.DRAFT)
                .build();
    }

    /**
     * Validate workflow data
     */
    public void validate() {
        if (weekStartDate == null || weekEndDate == null) {
            throw new IllegalArgumentException("Week start and end dates are required");
        }
        if (weekEndDate.isBefore(weekStartDate)) {
            throw new IllegalArgumentException("Week end date must be after start date");
        }
        if (totalHours == null || totalHours.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Total hours cannot be negative");
        }
        if (totalHours.compareTo(BigDecimal.valueOf(168)) > 0) { // 24 * 7 = 168 hours per week
            throw new IllegalArgumentException("Total hours cannot exceed 168 hours per week");
        }
    }
}
