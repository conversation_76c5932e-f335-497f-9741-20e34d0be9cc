import axios from 'axios';
import { getToken } from './authService';
import { API_BASE_URL } from '../config/apiConfig';

// Function to get token from multiple sources
const getAuthToken = (): string | null => {
  // Try tenant auth token first (for tenant-aware authentication)
  const tenantToken = localStorage.getItem('tenant_auth_token');
  if (tenantToken) {
    console.log('API Service: Using tenant auth token');
    return tenantToken;
  }

  // Fallback to regular auth token
  const regularToken = getToken();
  if (regularToken) {
    console.log('API Service: Using regular auth token');
    return regularToken;
  }

  console.log('API Service: No authentication token found');
  return null;
};

// Use centralized API configuration
console.log('API Service initialized with URL:', API_BASE_URL);

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to add auth token to requests
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url);

    // Get token from multiple sources (tenant auth first, then regular auth)
    const token = getAuthToken();
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
      console.log('API Request: Added Authorization header');
    } else {
      console.log('API Request: No authentication token found');
    }

    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor to handle common errors
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.config.url);
    return response;
  },
  (error) => {
    console.error('API Error:', error.response?.status, error.response?.data, error.config?.url);

    // Handle authentication errors
    if (error.response?.status === 401) {
      console.warn('Authentication error detected, redirecting to login');
      // Clear all auth data (both regular and tenant auth)
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth_user');
      localStorage.removeItem('tenant_auth_token');
      localStorage.removeItem('tenant_current_user');
      localStorage.removeItem('tenant_current_tenant');
      localStorage.removeItem('currentTenantId');

      // Only redirect if we're not already on the login page
      if (!window.location.pathname.includes('/login')) {
        window.location.href = '/login';
      }
    }

    // Handle server errors with user-friendly message
    if (error.response?.status >= 500) {
      error.userFriendlyMessage = 'The server encountered an error. Please try again later.';
    }

    return Promise.reject(error);
  }
);

export default api;
