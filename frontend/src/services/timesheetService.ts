import api from './apiService';
import { API_ENDPOINTS } from '../config/apiConfig';

// Timesheet Entry interfaces
export interface TimesheetEntry {
  id: number;
  user: {
    id: number;
    username: string;
    firstName: string;
    lastName: string;
  };
  issue: {
    id: number;
    identifier: string;
    title: string;
    type: string;
    status: string;
  };
  entryDate: string;
  hoursSpent: number;
  description: string;
  activityType: string;
  billable: boolean;
  isApproved: boolean;
  approvedBy?: {
    id: number;
    username: string;
    firstName: string;
    lastName: string;
  };
  approvedAt?: string;
  approvalNotes?: string;
  createdAt: string;
  updatedAt: string;
  activityTypeDisplay: string;
  approvalStatus: string;
  entryAgeDays: number;
}

export interface TimesheetEntryRequest {
  userId: number;
  issueId: number;
  entryDate: string;
  hoursSpent: number;
  description: string;
  activityType: string;
  billable?: boolean;
}

export interface TimesheetEntryUpdateRequest {
  hoursSpent?: number;
  description?: string;
  activityType?: string;
  billable?: boolean;
}

export interface ApprovalRequest {
  approverId: number;
  notes?: string;
}

export interface RejectionRequest {
  notes?: string;
}

export interface BulkApprovalRequest {
  entryIds: number[];
  approverId: number;
  notes?: string;
}

export interface BulkRejectionRequest {
  entryIds: number[];
  notes?: string;
}

export interface TimesheetDashboardData {
  totalHours: number;
  billableHours: number;
  dailySummary: Record<string, any>;
  activitySummary: Record<string, any>;
  issueSummary: Record<string, any>;
  currentWeekWorkflow?: any;
  todaysEntries: TimesheetEntry[];
}

export interface ValidationRules {
  maxHoursPerDay: number;
  minHoursPerEntry: number;
  maxDescriptionLength: number;
  canEditCurrentDayOnly: boolean;
  availableActivityTypes: string[];
}

// Timesheet Entry Management
const createTimesheetEntry = async (request: TimesheetEntryRequest): Promise<TimesheetEntry> => {
  console.log('Creating timesheet entry:', request);
  const response = await api.post(API_ENDPOINTS.TIMESHEET.ENTRIES, request);
  console.log('Timesheet entry created:', response.data);
  return response.data;
};

const updateTimesheetEntry = async (entryId: number, request: TimesheetEntryUpdateRequest): Promise<TimesheetEntry> => {
  console.log(`Updating timesheet entry ${entryId}:`, request);
  const response = await api.put(API_ENDPOINTS.TIMESHEET.ENTRY_BY_ID(entryId), request);
  console.log('Timesheet entry updated:', response.data);
  return response.data;
};

const deleteTimesheetEntry = async (entryId: number): Promise<void> => {
  console.log(`Deleting timesheet entry: ${entryId}`);
  await api.delete(API_ENDPOINTS.TIMESHEET.ENTRY_BY_ID(entryId));
  console.log('Timesheet entry deleted');
};

const getTimesheetEntry = async (entryId: number): Promise<TimesheetEntry> => {
  console.log(`Fetching timesheet entry: ${entryId}`);
  const response = await api.get(API_ENDPOINTS.TIMESHEET.ENTRY_BY_ID(entryId));
  console.log('Timesheet entry fetched:', response.data);
  return response.data;
};

const getTimesheetEntriesByUser = async (userId: number, params?: any): Promise<any> => {
  console.log(`Fetching timesheet entries for user ${userId}:`, params);
  const response = await api.get(API_ENDPOINTS.TIMESHEET.ENTRIES_BY_USER(userId), { params });
  console.log('User timesheet entries fetched:', response.data);
  return response.data;
};

const getTodaysTimesheetEntries = async (userId: number): Promise<TimesheetEntry[]> => {
  console.log(`Fetching today's timesheet entries for user: ${userId}`);
  const response = await api.get(API_ENDPOINTS.TIMESHEET.ENTRIES_BY_USER_TODAY(userId));
  console.log('Today\'s timesheet entries fetched:', response.data);
  return response.data;
};

const getTimesheetEntriesByIssue = async (issueId: number, params?: any): Promise<any> => {
  console.log(`Fetching timesheet entries for issue ${issueId}:`, params);
  const response = await api.get(API_ENDPOINTS.TIMESHEET.ENTRIES_BY_ISSUE(issueId), { params });
  console.log('Issue timesheet entries fetched:', response.data);
  return response.data;
};

const getAllTimesheetEntries = async (params?: any): Promise<any> => {
  console.log('Fetching all timesheet entries:', params);
  const response = await api.get(API_ENDPOINTS.TIMESHEET.ENTRIES, { params });
  console.log('All timesheet entries fetched:', response.data);
  return response.data;
};

const getPendingApprovalEntries = async (params?: any): Promise<any> => {
  console.log('Fetching pending approval entries:', params);
  const response = await api.get(API_ENDPOINTS.TIMESHEET.PENDING_APPROVAL, { params });
  console.log('Pending approval entries fetched:', response.data);
  return response.data;
};

// Approval Management
const approveTimesheetEntry = async (entryId: number, request: ApprovalRequest): Promise<TimesheetEntry> => {
  console.log(`Approving timesheet entry ${entryId}:`, request);
  const response = await api.post(API_ENDPOINTS.TIMESHEET.APPROVE_ENTRY(entryId), request);
  console.log('Timesheet entry approved:', response.data);
  return response.data;
};

const rejectTimesheetEntry = async (entryId: number, request: RejectionRequest): Promise<TimesheetEntry> => {
  console.log(`Rejecting timesheet entry ${entryId}:`, request);
  const response = await api.post(API_ENDPOINTS.TIMESHEET.REJECT_ENTRY(entryId), request);
  console.log('Timesheet entry rejected:', response.data);
  return response.data;
};

const bulkApproveTimesheetEntries = async (request: BulkApprovalRequest): Promise<any> => {
  console.log('Bulk approving timesheet entries:', request);
  const response = await api.post(API_ENDPOINTS.TIMESHEET.BULK_APPROVE, request);
  console.log('Timesheet entries bulk approved:', response.data);
  return response.data;
};

const bulkRejectTimesheetEntries = async (request: BulkRejectionRequest): Promise<any> => {
  console.log('Bulk rejecting timesheet entries:', request);
  const response = await api.post(API_ENDPOINTS.TIMESHEET.BULK_REJECT, request);
  console.log('Timesheet entries bulk rejected:', response.data);
  return response.data;
};

// Dashboard and Analytics
const getUserDashboardData = async (userId: number, params?: any): Promise<TimesheetDashboardData> => {
  console.log(`Fetching user dashboard data for user ${userId}:`, params);
  const response = await api.get(API_ENDPOINTS.TIMESHEET.USER_DASHBOARD(userId), { params });
  console.log('User dashboard data fetched:', response.data);
  return response.data;
};

const getAdminDashboardData = async (params?: any): Promise<any> => {
  console.log('Fetching admin dashboard data:', params);
  const response = await api.get(API_ENDPOINTS.TIMESHEET.ADMIN_DASHBOARD, { params });
  console.log('Admin dashboard data fetched:', response.data);
  return response.data;
};

const getValidationRules = async (): Promise<ValidationRules> => {
  console.log('Fetching timesheet validation rules');
  const response = await api.get(API_ENDPOINTS.TIMESHEET.VALIDATION_RULES);
  console.log('Validation rules fetched:', response.data);
  return response.data;
};

// Utility functions
const formatHours = (hours: number): string => {
  if (hours === 0) return '0h';
  if (hours < 1) return `${Math.round(hours * 60)}m`;
  
  const wholeHours = Math.floor(hours);
  const minutes = Math.round((hours - wholeHours) * 60);
  
  if (minutes === 0) return `${wholeHours}h`;
  return `${wholeHours}h ${minutes}m`;
};

const parseHoursInput = (input: string): number => {
  // Handle formats like "2h 30m", "2.5h", "150m", "2:30"
  const hourMinuteRegex = /(\d+)h\s*(\d+)m/;
  const hourOnlyRegex = /(\d+(?:\.\d+)?)h/;
  const minuteOnlyRegex = /(\d+)m/;
  const colonFormatRegex = /(\d+):(\d+)/;
  
  let match = input.match(hourMinuteRegex);
  if (match) {
    return parseInt(match[1]) + parseInt(match[2]) / 60;
  }
  
  match = input.match(hourOnlyRegex);
  if (match) {
    return parseFloat(match[1]);
  }
  
  match = input.match(minuteOnlyRegex);
  if (match) {
    return parseInt(match[1]) / 60;
  }
  
  match = input.match(colonFormatRegex);
  if (match) {
    return parseInt(match[1]) + parseInt(match[2]) / 60;
  }
  
  // Try parsing as decimal hours
  const decimal = parseFloat(input);
  if (!isNaN(decimal)) {
    return decimal;
  }
  
  throw new Error('Invalid time format. Use formats like "2h 30m", "2.5h", "150m", or "2:30"');
};

const getActivityTypeDisplayName = (activityType: string): string => {
  const displayNames: Record<string, string> = {
    DEVELOPMENT: 'Development',
    TESTING: 'Testing',
    CODE_REVIEW: 'Code Review',
    ANALYSIS: 'Analysis',
    DOCUMENTATION: 'Documentation',
    MEETING: 'Meeting',
    DEBUGGING: 'Debugging',
    DEPLOYMENT: 'Deployment',
    RESEARCH: 'Research',
    PLANNING: 'Planning',
    TRAINING: 'Training',
    SUPPORT: 'Support',
    OTHER: 'Other',
  };
  
  return displayNames[activityType] || activityType;
};

export default {
  // Entry Management
  createTimesheetEntry,
  updateTimesheetEntry,
  deleteTimesheetEntry,
  getTimesheetEntry,
  getTimesheetEntriesByUser,
  getTodaysTimesheetEntries,
  getTimesheetEntriesByIssue,
  getAllTimesheetEntries,
  getPendingApprovalEntries,
  
  // Approval Management
  approveTimesheetEntry,
  rejectTimesheetEntry,
  bulkApproveTimesheetEntries,
  bulkRejectTimesheetEntries,
  
  // Dashboard and Analytics
  getUserDashboardData,
  getAdminDashboardData,
  getValidationRules,
  
  // Utility Functions
  formatHours,
  parseHoursInput,
  getActivityTypeDisplayName,
};
