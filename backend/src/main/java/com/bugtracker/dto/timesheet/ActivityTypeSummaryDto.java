package com.bugtracker.dto.timesheet;

import java.math.BigDecimal;

/**
 * DTO for activity type summary data
 */
public class ActivityTypeSummaryDto {
    private String activityType;
    private Long entryCount;
    private BigDecimal totalHours;
    private BigDecimal averageHours;

    public ActivityTypeSummaryDto() {}

    public ActivityTypeSummaryDto(String activityType, Long entryCount, BigDecimal totalHours, BigDecimal averageHours) {
        this.activityType = activityType;
        this.entryCount = entryCount;
        this.totalHours = totalHours;
        this.averageHours = averageHours;
    }

    // Constructor for handling Double values from aggregate functions
    public ActivityTypeSummaryDto(String activityType, Long entryCount, Double totalHours, Double averageHours) {
        this.activityType = activityType;
        this.entryCount = entryCount;
        this.totalHours = totalHours != null ? BigDecimal.valueOf(totalHours) : BigDecimal.ZERO;
        this.averageHours = averageHours != null ? BigDecimal.valueOf(averageHours) : BigDecimal.ZERO;
    }

    // Getters and setters
    public String getActivityType() {
        return activityType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType;
    }

    public Long getEntryCount() {
        return entryCount;
    }

    public void setEntryCount(Long entryCount) {
        this.entryCount = entryCount;
    }

    public BigDecimal getTotalHours() {
        return totalHours;
    }

    public void setTotalHours(BigDecimal totalHours) {
        this.totalHours = totalHours;
    }

    public BigDecimal getAverageHours() {
        return averageHours;
    }

    public void setAverageHours(BigDecimal averageHours) {
        this.averageHours = averageHours;
    }
}
