-- Remove unique constraint to allow multiple timesheet entries for same activity type per day
-- Users should be able to log multiple time sessions for the same activity

-- Drop the Hibernate-generated unique constraint on timesheet_entries
-- This allows users to log multiple time entries for the same activity type on the same day
ALTER TABLE timesheet_entries DROP CONSTRAINT IF EXISTS ukh5gyafdqml1gy1qj0e9nqc39l;

-- Also drop any other potential constraint names
ALTER TABLE timesheet_entries DROP CONSTRAINT IF EXISTS timesheet_entries_user_id_issue_id_entry_date_activity_type_key;

-- Add comment explaining the change
COMMENT ON TABLE timesheet_entries IS 'Daily timesheet entries for user-friendly time logging. Users can log multiple entries for the same activity type on the same day to track different work sessions.';
