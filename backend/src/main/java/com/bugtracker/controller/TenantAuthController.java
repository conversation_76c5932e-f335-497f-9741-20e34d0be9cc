package com.bugtracker.controller;

import com.bugtracker.config.TenantContext;
import com.bugtracker.dto.LoginRequest;
import com.bugtracker.dto.SignupRequest;
import com.bugtracker.model.Tenant;
import com.bugtracker.repository.TenantRepository;
import com.bugtracker.security.jwt.JwtUtils;
import com.bugtracker.service.TenantAwareAuthService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Optional;

/**
 * Tenant-aware authentication controller
 * Handles login and registration within tenant context
 */
@Slf4j
@RestController
@RequestMapping("/api/tenant-auth")
@CrossOrigin(origins = "*", maxAge = 3600)
public class TenantAuthController {

    @Autowired
    private TenantAwareAuthService tenantAwareAuthService;

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private TenantRepository tenantRepository;

    /**
     * Tenant-aware user login
     */
    @PostMapping("/signin")
    public ResponseEntity<?> authenticateUser(@Valid @RequestBody LoginRequest loginRequest) {
        try {
            // Set tenant context manually from the request
            String tenantId = loginRequest.getTenantId();
            if (tenantId != null && !tenantId.trim().isEmpty()) {
                TenantContext.setCurrentTenant(tenantId);
                log.info("Authentication request for user {} in tenant {}", loginRequest.getUsername(), tenantId);
            } else {
                log.error("No tenant ID provided in login request");
                return ResponseEntity.badRequest()
                    .body(Map.of("error", "Tenant ID is required"));
            }

            TenantAwareAuthService.AuthenticationResult result =
                tenantAwareAuthService.authenticateUser(loginRequest);

            if (result.isSuccess()) {
                return ResponseEntity.ok(Map.of(
                    "token", result.getToken(),
                    "type", "Bearer",
                    "id", result.getUser().getId(),
                    "username", result.getUser().getUsername(),
                    "email", result.getUser().getEmail(),
                    "firstName", result.getUser().getFirstName(),
                    "lastName", result.getUser().getLastName(),
                    "roles", result.getRoles(),
                    "tenant", Map.of(
                        "id", result.getTenant().getTenantId(),
                        "name", result.getTenant().getName(),
                        "subdomain", result.getTenant().getSubdomain()
                    )
                ));
            } else {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", result.getMessage()));
            }

        } catch (Exception e) {
            log.error("Authentication error", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Authentication failed"));
        } finally {
            // Clear tenant context after authentication
            TenantContext.clear();
        }
    }

    /**
     * Tenant-aware user registration
     */
    @PostMapping("/signup")
    public ResponseEntity<?> registerUser(@Valid @RequestBody SignupRequest signupRequest) {
        try {
            String tenantId = TenantContext.getCurrentTenant();
            log.info("Registration request for user {} in tenant {}", signupRequest.getUsername(), tenantId);

            TenantAwareAuthService.RegistrationResult result =
                tenantAwareAuthService.registerUser(signupRequest);

            if (result.isSuccess()) {
                return ResponseEntity.status(HttpStatus.CREATED)
                    .body(Map.of(
                        "message", "User registered successfully",
                        "user", Map.of(
                            "id", result.getUser().getId(),
                            "username", result.getUser().getUsername(),
                            "email", result.getUser().getEmail(),
                            "firstName", result.getUser().getFirstName(),
                            "lastName", result.getUser().getLastName()
                        )
                    ));
            } else {
                return ResponseEntity.badRequest()
                    .body(Map.of("error", result.getMessage()));
            }

        } catch (Exception e) {
            log.error("Registration error", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Registration failed"));
        }
    }

    /**
     * Get current user information
     */
    @GetMapping("/me")
    public ResponseEntity<?> getCurrentUser(@RequestHeader(value = "X-User-ID", required = false) String userIdHeader) {
        try {
            String tenantId = TenantContext.getCurrentTenant();
            log.debug("Getting current user info for tenant {}", tenantId);

            // For now, since we don't have JWT token parsing, we'll use a default admin user
            // In a real implementation, this would extract user info from JWT token
            if (userIdHeader != null && !userIdHeader.trim().isEmpty()) {
                try {
                    Long userId = Long.parseLong(userIdHeader);
                    return tenantAwareAuthService.getCurrentUserInfo(userId);
                } catch (NumberFormatException e) {
                    log.warn("Invalid user ID header: {}", userIdHeader);
                }
            }

            // Default to admin user for the tenant (for demo purposes)
            return tenantAwareAuthService.getDefaultAdminUser();

        } catch (Exception e) {
            log.error("Error getting current user", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to get user information"));
        }
    }

    /**
     * Logout (clear any server-side session if needed)
     */
    @PostMapping("/logout")
    public ResponseEntity<?> logout() {
        try {
            // In a stateless JWT setup, logout is typically handled client-side
            // by removing the token. Server-side logout would involve token blacklisting
            log.info("Logout request for tenant {}", TenantContext.getCurrentTenant());

            return ResponseEntity.ok(Map.of("message", "Logged out successfully"));

        } catch (Exception e) {
            log.error("Logout error", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Logout failed"));
        }
    }

    /**
     * Validate token endpoint
     */
    @PostMapping("/validate")
    public ResponseEntity<?> validateToken(@RequestBody Map<String, String> request) {
        try {
            String token = request.get("token");
            if (token == null || token.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(Map.of("error", "Token is required"));
            }

            // Validate the JWT token
            if (!jwtUtils.validateJwtToken(token)) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", "Invalid token", "valid", false));
            }

            // Extract information from token
            String username = jwtUtils.getUserNameFromJwtToken(token);
            String tenantId = jwtUtils.getTenantIdFromJwtToken(token);
            Long userId = jwtUtils.getUserIdFromJwtToken(token);

            if (username == null || tenantId == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", "Invalid token claims", "valid", false));
            }

            // Set tenant context
            TenantContext.setCurrentTenant(tenantId);

            // Validate tenant exists and is active
            Optional<Tenant> tenantOpt = tenantRepository.findByTenantId(tenantId);
            if (tenantOpt.isEmpty()) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", "Tenant not found", "valid", false));
            }

            Tenant tenant = tenantOpt.get();
            if (!tenant.isActive()) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", "Tenant is not active", "valid", false));
            }

            if (!tenant.isSubscriptionValid()) {
                return ResponseEntity.status(HttpStatus.PAYMENT_REQUIRED)
                    .body(Map.of("error", "Subscription expired", "valid", false));
            }

            // Return validation success with user/tenant info
            return ResponseEntity.ok(Map.of(
                "valid", true,
                "username", username,
                "tenantId", tenantId,
                "userId", userId,
                "tenant", Map.of(
                    "id", tenant.getTenantId(),
                    "name", tenant.getName(),
                    "subdomain", tenant.getSubdomain()
                )
            ));

        } catch (Exception e) {
            log.error("Token validation error", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Token validation failed", "valid", false));
        } finally {
            // Clear tenant context
            TenantContext.clear();
        }
    }

    /**
     * Refresh token endpoint
     */
    @PostMapping("/refresh")
    public ResponseEntity<?> refreshToken(@RequestBody Map<String, String> request) {
        try {
            String refreshToken = request.get("refreshToken");
            if (refreshToken == null || refreshToken.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(Map.of("error", "Refresh token is required"));
            }

            // Here you would validate the refresh token and generate a new access token
            // For now, return a placeholder response
            return ResponseEntity.ok(Map.of(
                "message", "Token refresh endpoint",
                "tenantId", TenantContext.getCurrentTenant()
            ));

        } catch (Exception e) {
            log.error("Token refresh error", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Token refresh failed"));
        }
    }

    /**
     * Check tenant status
     */
    @GetMapping("/tenant-status")
    public ResponseEntity<?> getTenantStatus() {
        try {
            String tenantId = TenantContext.getCurrentTenant();
            String tenantSchema = TenantContext.getCurrentTenantSchema();
            Long tenantDbId = TenantContext.getCurrentTenantId();

            return ResponseEntity.ok(Map.of(
                "tenantId", tenantId,
                "tenantSchema", tenantSchema,
                "tenantDbId", tenantDbId,
                "contextInfo", TenantContext.getContextInfo(),
                "status", "active"
            ));

        } catch (Exception e) {
            log.error("Error getting tenant status", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to get tenant status"));
        }
    }
}
