package com.bugtracker.controller;

import com.bugtracker.model.TimesheetEntry;
import com.bugtracker.model.TimesheetApprovalWorkflow;
import com.bugtracker.service.TimesheetService;
import com.bugtracker.security.jwt.JwtUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * REST Controller for Timesheet Management
 */
@RestController
@RequestMapping("/api/timesheet")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*", maxAge = 3600)
public class TimesheetController {

    private final TimesheetService timesheetService;
    private final JwtUtils jwtUtils;

    // ===== Timesheet Entry Management =====

    /**
     * Create a new timesheet entry
     */
    @PostMapping("/entries")
    public ResponseEntity<?> createTimesheetEntry(@Valid @RequestBody TimesheetEntryRequest request,
                                                 HttpServletRequest httpRequest) {
        try {
            // Extract user ID from JWT token for security
            Long authenticatedUserId = extractUserIdFromJwt(httpRequest);
            if (authenticatedUserId == null) {
                log.error("No authenticated user found in JWT token");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(Map.of("error", "Authentication required"));
            }

            log.info("Creating timesheet entry for authenticated user: {}, issue: {}",
                    authenticatedUserId, request.getIssueId());

            TimesheetEntry entry = timesheetService.createTimesheetEntry(
                    authenticatedUserId, // Use authenticated user ID from JWT token
                    request.getIssueId(),
                    request.getEntryDate(),
                    request.getHoursSpent(),
                    request.getDescription(),
                    request.getActivityType(),
                    request.getBillable()
            );

            return ResponseEntity.status(HttpStatus.CREATED).body(entry);
        } catch (IllegalArgumentException e) {
            log.error("Invalid timesheet entry request: {}", e.getMessage());
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            log.error("Failed to create timesheet entry: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to create timesheet entry: " + e.getMessage()));
        }
    }

    /**
     * Update an existing timesheet entry
     */
    @PutMapping("/entries/{entryId}")
    public ResponseEntity<?> updateTimesheetEntry(@PathVariable Long entryId, 
                                                 @Valid @RequestBody TimesheetEntryUpdateRequest request) {
        try {
            log.info("Updating timesheet entry: {}", entryId);
            
            TimesheetEntry entry = timesheetService.updateTimesheetEntry(
                    entryId,
                    request.getHoursSpent(),
                    request.getDescription(),
                    request.getActivityType(),
                    request.getBillable()
            );
            
            return ResponseEntity.ok(entry);
        } catch (IllegalStateException e) {
            log.error("Cannot update timesheet entry: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            log.error("Failed to update timesheet entry: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to update timesheet entry: " + e.getMessage()));
        }
    }

    /**
     * Delete a timesheet entry
     */
    @DeleteMapping("/entries/{entryId}")
    public ResponseEntity<?> deleteTimesheetEntry(@PathVariable Long entryId) {
        try {
            log.info("Deleting timesheet entry: {}", entryId);
            timesheetService.deleteTimesheetEntry(entryId);
            return ResponseEntity.ok(Map.of("message", "Timesheet entry deleted successfully"));
        } catch (IllegalStateException e) {
            log.error("Cannot delete timesheet entry: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            log.error("Failed to delete timesheet entry: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to delete timesheet entry: " + e.getMessage()));
        }
    }

    /**
     * Get timesheet entry by ID
     */
    @GetMapping("/entries/{entryId}")
    public ResponseEntity<?> getTimesheetEntry(@PathVariable Long entryId) {
        try {
            Optional<TimesheetEntry> entry = timesheetService.getTimesheetEntryById(entryId);
            return entry.map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            log.error("Failed to get timesheet entry: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to get timesheet entry: " + e.getMessage()));
        }
    }

    /**
     * Get timesheet entries for a user
     */
    @GetMapping("/entries/user/{userId}")
    public ResponseEntity<?> getTimesheetEntriesByUser(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<TimesheetEntry> entries;
            
            if (startDate != null && endDate != null) {
                entries = timesheetService.getTimesheetEntriesByUserAndDateRange(userId, startDate, endDate, pageable);
            } else {
                entries = timesheetService.getTimesheetEntriesByUser(userId, pageable);
            }
            
            return ResponseEntity.ok(entries);
        } catch (Exception e) {
            log.error("Failed to get timesheet entries for user: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to get timesheet entries: " + e.getMessage()));
        }
    }

    /**
     * Get today's timesheet entries for a user
     */
    @GetMapping("/entries/user/{userId}/today")
    public ResponseEntity<?> getTodaysTimesheetEntries(@PathVariable Long userId) {
        try {
            List<TimesheetEntry> entries = timesheetService.getTodaysTimesheetEntries(userId);
            return ResponseEntity.ok(entries);
        } catch (Exception e) {
            log.error("Failed to get today's timesheet entries: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to get today's timesheet entries: " + e.getMessage()));
        }
    }

    /**
     * Get timesheet entries for an issue
     */
    @GetMapping("/entries/issue/{issueId}")
    public ResponseEntity<?> getTimesheetEntriesByIssue(
            @PathVariable Long issueId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<TimesheetEntry> entries = timesheetService.getTimesheetEntriesByIssue(issueId, pageable);
            return ResponseEntity.ok(entries);
        } catch (Exception e) {
            log.error("Failed to get timesheet entries for issue: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to get timesheet entries: " + e.getMessage()));
        }
    }

    /**
     * Get all timesheet entries (admin only)
     */
    @GetMapping("/entries")
    public ResponseEntity<?> getAllTimesheetEntries(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<TimesheetEntry> entries;
            
            if (startDate != null && endDate != null) {
                entries = timesheetService.getTimesheetEntriesByDateRange(startDate, endDate, pageable);
            } else {
                entries = timesheetService.getAllTimesheetEntries(pageable);
            }
            
            return ResponseEntity.ok(entries);
        } catch (Exception e) {
            log.error("Failed to get all timesheet entries: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to get timesheet entries: " + e.getMessage()));
        }
    }

    /**
     * Get pending approval entries
     */
    @GetMapping("/entries/pending-approval")
    public ResponseEntity<?> getPendingApprovalEntries(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) Long userId) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<TimesheetEntry> entries;
            
            if (userId != null) {
                entries = timesheetService.getPendingApprovalEntriesByUser(userId, pageable);
            } else {
                entries = timesheetService.getPendingApprovalEntries(pageable);
            }
            
            return ResponseEntity.ok(entries);
        } catch (Exception e) {
            log.error("Failed to get pending approval entries: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to get pending approval entries: " + e.getMessage()));
        }
    }

    // ===== Request DTOs =====

    public static class TimesheetEntryRequest {
        // userId is now extracted from JWT token for security
        private Long issueId;
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
        private LocalDate entryDate;
        private BigDecimal hoursSpent;
        private String description;
        private String activityType;
        private Boolean billable;

        // Getters and setters
        public Long getIssueId() { return issueId; }
        public void setIssueId(Long issueId) { this.issueId = issueId; }
        public LocalDate getEntryDate() { return entryDate; }
        public void setEntryDate(LocalDate entryDate) { this.entryDate = entryDate; }
        public BigDecimal getHoursSpent() { return hoursSpent; }
        public void setHoursSpent(BigDecimal hoursSpent) { this.hoursSpent = hoursSpent; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getActivityType() { return activityType; }
        public void setActivityType(String activityType) { this.activityType = activityType; }
        public Boolean getBillable() { return billable; }
        public void setBillable(Boolean billable) { this.billable = billable; }
    }

    public static class TimesheetEntryUpdateRequest {
        private BigDecimal hoursSpent;
        private String description;
        private String activityType;
        private Boolean billable;

        // Getters and setters
        public BigDecimal getHoursSpent() { return hoursSpent; }
        public void setHoursSpent(BigDecimal hoursSpent) { this.hoursSpent = hoursSpent; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getActivityType() { return activityType; }
        public void setActivityType(String activityType) { this.activityType = activityType; }
        public Boolean getBillable() { return billable; }
        public void setBillable(Boolean billable) { this.billable = billable; }
    }

    // ===== Approval Management =====

    /**
     * Approve timesheet entry
     */
    @PostMapping("/entries/{entryId}/approve")
    public ResponseEntity<?> approveTimesheetEntry(@PathVariable Long entryId,
                                                  @RequestBody ApprovalRequest request) {
        try {
            log.info("Approving timesheet entry: {} by user: {}", entryId, request.getApproverId());

            TimesheetEntry entry = timesheetService.approveTimesheetEntry(
                    entryId, request.getApproverId(), request.getNotes());

            return ResponseEntity.ok(entry);
        } catch (IllegalStateException e) {
            log.error("Cannot approve timesheet entry: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            log.error("Failed to approve timesheet entry: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to approve timesheet entry: " + e.getMessage()));
        }
    }

    /**
     * Reject timesheet entry
     */
    @PostMapping("/entries/{entryId}/reject")
    public ResponseEntity<?> rejectTimesheetEntry(@PathVariable Long entryId,
                                                 @RequestBody RejectionRequest request) {
        try {
            log.info("Rejecting timesheet entry: {}", entryId);

            TimesheetEntry entry = timesheetService.rejectTimesheetEntry(entryId, request.getNotes());

            return ResponseEntity.ok(entry);
        } catch (Exception e) {
            log.error("Failed to reject timesheet entry: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to reject timesheet entry: " + e.getMessage()));
        }
    }

    /**
     * Bulk approve timesheet entries
     */
    @PostMapping("/entries/bulk-approve")
    public ResponseEntity<?> bulkApproveTimesheetEntries(@RequestBody BulkApprovalRequest request) {
        try {
            log.info("Bulk approving {} timesheet entries by user: {}",
                    request.getEntryIds().size(), request.getApproverId());

            List<TimesheetEntry> entries = timesheetService.bulkApproveTimesheetEntries(
                    request.getEntryIds(), request.getApproverId(), request.getNotes());

            return ResponseEntity.ok(Map.of(
                    "message", "Successfully approved " + entries.size() + " timesheet entries",
                    "entries", entries
            ));
        } catch (IllegalStateException e) {
            log.error("Cannot bulk approve timesheet entries: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            log.error("Failed to bulk approve timesheet entries: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to bulk approve timesheet entries: " + e.getMessage()));
        }
    }

    /**
     * Bulk reject timesheet entries
     */
    @PostMapping("/entries/bulk-reject")
    public ResponseEntity<?> bulkRejectTimesheetEntries(@RequestBody BulkRejectionRequest request) {
        try {
            log.info("Bulk rejecting {} timesheet entries", request.getEntryIds().size());

            List<TimesheetEntry> entries = timesheetService.bulkRejectTimesheetEntries(
                    request.getEntryIds(), request.getNotes());

            return ResponseEntity.ok(Map.of(
                    "message", "Successfully rejected " + entries.size() + " timesheet entries",
                    "entries", entries
            ));
        } catch (Exception e) {
            log.error("Failed to bulk reject timesheet entries: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to bulk reject timesheet entries: " + e.getMessage()));
        }
    }

    // ===== Analytics and Reporting =====

    /**
     * Get user dashboard data
     */
    @GetMapping("/dashboard/user/{userId}")
    public ResponseEntity<?> getUserDashboardData(
            @PathVariable Long userId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            // Default to current month if dates not provided
            if (startDate == null) {
                startDate = LocalDate.now().withDayOfMonth(1);
            }
            if (endDate == null) {
                endDate = LocalDate.now();
            }

            Map<String, Object> dashboardData = timesheetService.getUserDashboardData(userId, startDate, endDate);
            return ResponseEntity.ok(dashboardData);
        } catch (Exception e) {
            log.error("Failed to get user dashboard data: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to get dashboard data: " + e.getMessage()));
        }
    }

    /**
     * Get admin dashboard data
     */
    @GetMapping("/dashboard/admin")
    public ResponseEntity<?> getAdminDashboardData(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            // Default to current month if dates not provided
            if (startDate == null) {
                startDate = LocalDate.now().withDayOfMonth(1);
            }
            if (endDate == null) {
                endDate = LocalDate.now();
            }

            Map<String, Object> dashboardData = timesheetService.getAdminDashboardData(startDate, endDate);
            return ResponseEntity.ok(dashboardData);
        } catch (Exception e) {
            log.error("Failed to get admin dashboard data: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to get admin dashboard data: " + e.getMessage()));
        }
    }

    /**
     * Get validation rules
     */
    @GetMapping("/validation-rules")
    public ResponseEntity<?> getValidationRules() {
        try {
            Map<String, Object> rules = timesheetService.getValidationRules();
            return ResponseEntity.ok(rules);
        } catch (Exception e) {
            log.error("Failed to get validation rules: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to get validation rules: " + e.getMessage()));
        }
    }

    // ===== Additional Request DTOs =====

    public static class ApprovalRequest {
        private Long approverId;
        private String notes;

        public Long getApproverId() { return approverId; }
        public void setApproverId(Long approverId) { this.approverId = approverId; }
        public String getNotes() { return notes; }
        public void setNotes(String notes) { this.notes = notes; }
    }

    public static class RejectionRequest {
        private String notes;

        public String getNotes() { return notes; }
        public void setNotes(String notes) { this.notes = notes; }
    }

    public static class BulkApprovalRequest {
        private List<Long> entryIds;
        private Long approverId;
        private String notes;

        public List<Long> getEntryIds() { return entryIds; }
        public void setEntryIds(List<Long> entryIds) { this.entryIds = entryIds; }
        public Long getApproverId() { return approverId; }
        public void setApproverId(Long approverId) { this.approverId = approverId; }
        public String getNotes() { return notes; }
        public void setNotes(String notes) { this.notes = notes; }
    }

    public static class BulkRejectionRequest {
        private List<Long> entryIds;
        private String notes;

        public List<Long> getEntryIds() { return entryIds; }
        public void setEntryIds(List<Long> entryIds) { this.entryIds = entryIds; }
        public String getNotes() { return notes; }
        public void setNotes(String notes) { this.notes = notes; }
    }

    // ===== Helper Methods =====

    /**
     * Extract user ID from JWT token in the request
     */
    private Long extractUserIdFromJwt(HttpServletRequest request) {
        try {
            String authHeader = request.getHeader("Authorization");
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                String token = authHeader.substring(7);
                if (jwtUtils.validateJwtToken(token)) {
                    Long userId = jwtUtils.getUserIdFromJwtToken(token);
                    log.debug("Extracted user ID from JWT token: {}", userId);
                    return userId;
                }
            }
            log.warn("No valid JWT token found in request");
            return null;
        } catch (Exception e) {
            log.error("Error extracting user ID from JWT token: {}", e.getMessage());
            return null;
        }
    }
}
