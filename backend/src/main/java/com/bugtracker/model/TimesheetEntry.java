package com.bugtracker.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Timesheet Entry entity for daily time logging
 * This provides a user-friendly interface for logging daily time spent on issues
 */
@Entity
@Table(name = "timesheet_entries", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"user_id", "issue_id", "entry_date", "activity_type"}))
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class TimesheetEntry {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "user_id", nullable = false)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "roles", "password"})
    private User user;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "issue_id", nullable = false)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "timeTrackingEntries", "effortEstimations", "attachments", "comments", "watchers"})
    private Issue issue;

    @Column(name = "entry_date", nullable = false)
    private LocalDate entryDate;

    @Column(name = "hours_spent", nullable = false, precision = 5, scale = 2)
    private BigDecimal hoursSpent;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String description;

    @Column(name = "activity_type", nullable = false, length = 50)
    @Builder.Default
    private String activityType = "DEVELOPMENT";

    @Column(nullable = false)
    @Builder.Default
    private Boolean billable = true;

    @Column(name = "is_approved", nullable = false)
    @Builder.Default
    private Boolean isApproved = false;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "approved_by")
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "roles", "password"})
    private User approvedBy;

    @Column(name = "approved_at")
    private LocalDateTime approvedAt;

    @Column(name = "approval_notes", columnDefinition = "TEXT")
    private String approvalNotes;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * Activity types enum for validation
     */
    public enum ActivityType {
        DEVELOPMENT("Development"),
        TESTING("Testing"),
        CODE_REVIEW("Code Review"),
        ANALYSIS("Analysis"),
        DOCUMENTATION("Documentation"),
        MEETING("Meeting"),
        DEBUGGING("Debugging"),
        DEPLOYMENT("Deployment"),
        RESEARCH("Research"),
        PLANNING("Planning"),
        TRAINING("Training"),
        SUPPORT("Support"),
        OTHER("Other");

        private final String displayName;

        ActivityType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }

        @Override
        public String toString() {
            return this.name();
        }
    }

    /**
     * Check if this entry can be edited
     * Only current day entries can be modified
     */
    public boolean isEditable() {
        return entryDate.equals(LocalDate.now()) && !isApproved;
    }

    /**
     * Check if this entry is from today
     */
    public boolean isFromToday() {
        return entryDate.equals(LocalDate.now());
    }

    /**
     * Check if this entry is overdue for approval
     */
    public boolean isOverdueForApproval() {
        return !isApproved && entryDate.isBefore(LocalDate.now().minusDays(7));
    }

    /**
     * Approve this timesheet entry
     */
    public void approve(User approver, String notes) {
        this.isApproved = true;
        this.approvedBy = approver;
        this.approvedAt = LocalDateTime.now();
        this.approvalNotes = notes;
    }

    /**
     * Reject this timesheet entry (unapprove)
     */
    public void reject(String notes) {
        this.isApproved = false;
        this.approvedBy = null;
        this.approvedAt = null;
        this.approvalNotes = notes;
    }

    /**
     * Get hours as double for calculations
     */
    public Double getHoursAsDouble() {
        return hoursSpent != null ? hoursSpent.doubleValue() : 0.0;
    }

    /**
     * Set hours from double
     */
    public void setHoursFromDouble(Double hours) {
        this.hoursSpent = hours != null ? BigDecimal.valueOf(hours) : BigDecimal.ZERO;
    }

    /**
     * Get formatted activity type for display
     */
    @JsonProperty("activityTypeDisplay")
    public String getActivityTypeDisplay() {
        try {
            return ActivityType.valueOf(activityType).getDisplayName();
        } catch (IllegalArgumentException e) {
            return activityType; // Return as-is if not in enum
        }
    }

    /**
     * Get approval status as string
     */
    @JsonProperty("approvalStatus")
    public String getApprovalStatus() {
        if (isApproved) {
            return "APPROVED";
        } else if (isOverdueForApproval()) {
            return "OVERDUE";
        } else {
            return "PENDING";
        }
    }

    /**
     * Get entry age in days
     */
    @JsonProperty("entryAgeDays")
    public long getEntryAgeDays() {
        return java.time.temporal.ChronoUnit.DAYS.between(entryDate, LocalDate.now());
    }

    /**
     * Validate timesheet entry data
     */
    public void validate() {
        if (hoursSpent == null || hoursSpent.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Hours spent must be greater than 0");
        }
        if (hoursSpent.compareTo(BigDecimal.valueOf(24)) > 0) {
            throw new IllegalArgumentException("Hours spent cannot exceed 24 hours per day");
        }
        if (description == null || description.trim().isEmpty()) {
            throw new IllegalArgumentException("Description is required");
        }
        if (entryDate == null) {
            throw new IllegalArgumentException("Entry date is required");
        }
        if (entryDate.isAfter(LocalDate.now())) {
            throw new IllegalArgumentException("Cannot log time for future dates");
        }
    }

    /**
     * Create a copy of this entry for a different date
     */
    public TimesheetEntry copyForDate(LocalDate newDate) {
        return TimesheetEntry.builder()
                .user(this.user)
                .issue(this.issue)
                .entryDate(newDate)
                .hoursSpent(this.hoursSpent)
                .description(this.description)
                .activityType(this.activityType)
                .billable(this.billable)
                .isApproved(false) // New entries are not approved
                .build();
    }
}
