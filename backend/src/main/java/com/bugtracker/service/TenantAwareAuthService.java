package com.bugtracker.service;

import com.bugtracker.config.TenantContext;
import com.bugtracker.dto.LoginRequest;
import com.bugtracker.dto.SignupRequest;
import com.bugtracker.model.Role;
import com.bugtracker.model.Tenant;
import com.bugtracker.model.User;
import com.bugtracker.repository.RoleRepository;
import com.bugtracker.repository.TenantRepository;
import com.bugtracker.repository.UserRepository;
import com.bugtracker.security.jwt.JwtUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Tenant-aware authentication service
 * Handles login and registration within tenant context
 */
@Slf4j
@Service
public class TenantAwareAuthService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private TenantRepository tenantRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private DataSource dataSource;

    /**
     * Authenticate user within tenant context
     */
    public AuthenticationResult authenticateUser(LoginRequest loginRequest) {
        String tenantId = TenantContext.getCurrentTenant();

        if (tenantId == null) {
            log.error("No tenant context found during authentication");
            return AuthenticationResult.failure("No tenant context");
        }

        log.info("Authenticating user {} for tenant {}", loginRequest.getUsername(), tenantId);

        try {
            // Validate tenant
            Optional<Tenant> tenantOpt = tenantRepository.findByTenantId(tenantId);
            if (tenantOpt.isEmpty()) {
                log.error("Tenant not found: {}", tenantId);
                return AuthenticationResult.failure("Tenant not found");
            }

            Tenant tenant = tenantOpt.get();
            if (!tenant.isActive()) {
                log.error("Tenant is not active: {}", tenantId);
                return AuthenticationResult.failure("Tenant is not active");
            }

            if (!tenant.isSubscriptionValid()) {
                log.error("Tenant subscription expired: {}", tenantId);
                return AuthenticationResult.failure("Subscription expired");
            }

            // Find user in tenant schema using raw SQL
            User user = findUserInTenantSchema(loginRequest.getUsername(), tenant.getSchemaName());
            if (user == null) {
                log.error("User not found: {} in tenant {}", loginRequest.getUsername(), tenantId);
                return AuthenticationResult.failure("Invalid credentials");
            }

            // Validate password
            if (!passwordEncoder.matches(loginRequest.getPassword(), user.getPassword())) {
                log.error("Invalid password for user: {} in tenant {}", loginRequest.getUsername(), tenantId);
                return AuthenticationResult.failure("Invalid credentials");
            }

            // Generate JWT token with tenant information
            String token = jwtUtils.generateJwtToken(user, tenantId);

            // Get user roles from tenant schema
            List<String> roles = getUserRolesFromTenantSchema(user.getId(), tenant.getSchemaName());

            log.info("User authenticated successfully: {} for tenant {}", user.getUsername(), tenantId);

            return AuthenticationResult.success(token, user, roles, tenant);

        } catch (Exception e) {
            log.error("Authentication error for user {} in tenant {}", loginRequest.getUsername(), tenantId, e);
            return AuthenticationResult.failure("Authentication failed");
        }
    }

    /**
     * Register new user within tenant context
     */
    @Transactional
    public RegistrationResult registerUser(SignupRequest signupRequest) {
        String tenantId = TenantContext.getCurrentTenant();

        if (tenantId == null) {
            log.error("No tenant context found during registration");
            return RegistrationResult.failure("No tenant context");
        }

        log.info("Registering user {} for tenant {}", signupRequest.getUsername(), tenantId);

        try {
            // Validate tenant
            Optional<Tenant> tenantOpt = tenantRepository.findByTenantId(tenantId);
            if (tenantOpt.isEmpty()) {
                log.error("Tenant not found: {}", tenantId);
                return RegistrationResult.failure("Tenant not found");
            }

            Tenant tenant = tenantOpt.get();
            if (!tenant.isActive()) {
                log.error("Tenant is not active: {}", tenantId);
                return RegistrationResult.failure("Tenant is not active");
            }

            // Check user limits
            long currentUserCount = userRepository.count();
            if (!tenant.canAddMoreUsers((int) currentUserCount)) {
                log.error("User limit exceeded for tenant: {}", tenantId);
                return RegistrationResult.failure("User limit exceeded");
            }

            // Check if username already exists in tenant
            if (userRepository.existsByUsername(signupRequest.getUsername())) {
                log.error("Username already exists: {} in tenant {}", signupRequest.getUsername(), tenantId);
                return RegistrationResult.failure("Username already exists");
            }

            // Check if email already exists in tenant
            if (userRepository.existsByEmail(signupRequest.getEmail())) {
                log.error("Email already exists: {} in tenant {}", signupRequest.getEmail(), tenantId);
                return RegistrationResult.failure("Email already exists");
            }

            // Create user
            User user = User.builder()
                .username(signupRequest.getUsername())
                .email(signupRequest.getEmail())
                .password(passwordEncoder.encode(signupRequest.getPassword()))
                .firstName(signupRequest.getFirstName())
                .lastName(signupRequest.getLastName())
                .department(signupRequest.getDepartment())
                .build();

            // Assign default role (DEVELOPER for regular users)
            Set<Role> roles = new HashSet<>();
            Role defaultRole = roleRepository.findByNameIgnoreCase("ROLE_DEVELOPER")
                .orElseThrow(() -> new RuntimeException("Default role not found"));
            roles.add(defaultRole);
            user.setRoles(roles);

            // Save user
            user = userRepository.save(user);

            log.info("User registered successfully: {} for tenant {}", user.getUsername(), tenantId);

            return RegistrationResult.success(user);

        } catch (Exception e) {
            log.error("Registration error for user {} in tenant {}", signupRequest.getUsername(), tenantId, e);
            return RegistrationResult.failure("Registration failed");
        }
    }

    /**
     * Create tenant admin user during tenant setup using raw SQL
     */
    @Transactional
    public User createTenantAdmin(Tenant tenant, String username, String email, String password,
                                 String firstName, String lastName) {
        log.info("Creating admin user for tenant: {}", tenant.getTenantId());

        String schemaName = tenant.getSchemaName();

        try (Connection connection = dataSource.getConnection()) {
            // Set search path to tenant schema
            try (PreparedStatement setSchemaStmt = connection.prepareStatement("SET search_path TO " + schemaName)) {
                setSchemaStmt.execute();
            }

            // First, get the admin role ID
            Long adminRoleId;
            try (PreparedStatement roleStmt = connection.prepareStatement(
                    "SELECT id FROM roles WHERE name = 'ROLE_ADMIN'")) {
                ResultSet roleRs = roleStmt.executeQuery();
                if (roleRs.next()) {
                    adminRoleId = roleRs.getLong("id");
                } else {
                    throw new RuntimeException("Admin role not found in tenant schema: " + schemaName);
                }
            }

            // Create the admin user
            Long userId;
            try (PreparedStatement userStmt = connection.prepareStatement(
                    "INSERT INTO users (username, email, first_name, last_name, password, created_at, department) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING id")) {
                userStmt.setString(1, username);
                userStmt.setString(2, email);
                userStmt.setString(3, firstName);
                userStmt.setString(4, lastName);
                userStmt.setString(5, passwordEncoder.encode(password));
                userStmt.setObject(6, LocalDateTime.now());
                userStmt.setString(7, "Administration");

                ResultSet userRs = userStmt.executeQuery();
                if (userRs.next()) {
                    userId = userRs.getLong("id");
                } else {
                    throw new RuntimeException("Failed to create admin user");
                }
            }

            // Assign admin role to user
            try (PreparedStatement userRoleStmt = connection.prepareStatement(
                    "INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)")) {
                userRoleStmt.setLong(1, userId);
                userRoleStmt.setLong(2, adminRoleId);
                userRoleStmt.execute();
            }

            log.info("Admin user created successfully for tenant: {} with ID: {}", tenant.getTenantId(), userId);

            // Return a simple User object (for logging purposes)
            User adminUser = new User();
            adminUser.setId(userId);
            adminUser.setUsername(username);
            adminUser.setEmail(email);
            adminUser.setFirstName(firstName);
            adminUser.setLastName(lastName);
            adminUser.setDepartment("Administration");

            return adminUser;

        } catch (SQLException e) {
            log.error("Failed to create admin user for tenant: {}", tenant.getTenantId(), e);
            throw new RuntimeException("Failed to create tenant admin user", e);
        }
    }

    /**
     * Find user in tenant schema using raw SQL
     */
    private User findUserInTenantSchema(String username, String schemaName) {
        try (Connection connection = dataSource.getConnection()) {
            // Set search path to tenant schema
            try (PreparedStatement setSchemaStmt = connection.prepareStatement("SET search_path TO " + schemaName)) {
                setSchemaStmt.execute();
            }

            // Find user by username
            try (PreparedStatement userStmt = connection.prepareStatement(
                    "SELECT id, username, email, first_name, last_name, password, department, created_at " +
                    "FROM users WHERE username = ?")) {
                userStmt.setString(1, username);

                ResultSet rs = userStmt.executeQuery();
                if (rs.next()) {
                    User user = new User();
                    user.setId(rs.getLong("id"));
                    user.setUsername(rs.getString("username"));
                    user.setEmail(rs.getString("email"));
                    user.setFirstName(rs.getString("first_name"));
                    user.setLastName(rs.getString("last_name"));
                    user.setPassword(rs.getString("password"));
                    user.setDepartment(rs.getString("department"));
                    user.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
                    return user;
                }
            }
        } catch (SQLException e) {
            log.error("Error finding user in tenant schema: {}", schemaName, e);
        }
        return null;
    }

    /**
     * Get user roles from tenant schema using raw SQL
     */
    private List<String> getUserRolesFromTenantSchema(Long userId, String schemaName) {
        List<String> roles = new ArrayList<>();
        try (Connection connection = dataSource.getConnection()) {
            // Set search path to tenant schema
            try (PreparedStatement setSchemaStmt = connection.prepareStatement("SET search_path TO " + schemaName)) {
                setSchemaStmt.execute();
            }

            // Get user roles
            try (PreparedStatement roleStmt = connection.prepareStatement(
                    "SELECT r.name FROM roles r " +
                    "JOIN user_roles ur ON r.id = ur.role_id " +
                    "WHERE ur.user_id = ?")) {
                roleStmt.setLong(1, userId);

                ResultSet rs = roleStmt.executeQuery();
                while (rs.next()) {
                    roles.add(rs.getString("name"));
                }
            }
        } catch (SQLException e) {
            log.error("Error getting user roles from tenant schema: {}", schemaName, e);
        }
        return roles;
    }

    /**
     * Get current user information by user ID
     */
    public ResponseEntity<?> getCurrentUserInfo(Long userId) {
        String tenantId = TenantContext.getCurrentTenant();
        if (tenantId == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(Map.of("error", "No tenant context"));
        }

        try {
            String schemaName = "tenant_" + tenantId;

            try (Connection connection = dataSource.getConnection()) {
                // Set search path to tenant schema
                try (PreparedStatement setSchemaStmt = connection.prepareStatement("SET search_path TO " + schemaName)) {
                    setSchemaStmt.execute();
                }

                // Get user with roles
                try (PreparedStatement userStmt = connection.prepareStatement(
                        "SELECT u.id, u.username, u.email, u.first_name, u.last_name, u.department " +
                        "FROM users u WHERE u.id = ?")) {
                    userStmt.setLong(1, userId);

                    ResultSet userRs = userStmt.executeQuery();
                    if (userRs.next()) {
                        // Get user roles
                        List<String> roles = getUserRolesFromTenantSchema(userId, schemaName);

                        Map<String, Object> userInfo = Map.of(
                            "id", userRs.getLong("id"),
                            "username", userRs.getString("username"),
                            "email", userRs.getString("email"),
                            "firstName", userRs.getString("first_name"),
                            "lastName", userRs.getString("last_name"),
                            "department", userRs.getString("department"),
                            "roles", roles,
                            "tenantId", tenantId
                        );

                        return ResponseEntity.ok(userInfo);
                    } else {
                        return ResponseEntity.notFound().build();
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error getting user info for user ID: {} in tenant: {}", userId, tenantId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to get user information"));
        }
    }

    /**
     * Get default admin user for the current tenant
     */
    public ResponseEntity<?> getDefaultAdminUser() {
        String tenantId = TenantContext.getCurrentTenant();
        if (tenantId == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(Map.of("error", "No tenant context"));
        }

        try {
            String schemaName = "tenant_" + tenantId;
            String adminUsername = tenantId + "_admin";

            try (Connection connection = dataSource.getConnection()) {
                // Set search path to tenant schema
                try (PreparedStatement setSchemaStmt = connection.prepareStatement("SET search_path TO " + schemaName)) {
                    setSchemaStmt.execute();
                }

                // Get admin user
                try (PreparedStatement userStmt = connection.prepareStatement(
                        "SELECT u.id, u.username, u.email, u.first_name, u.last_name, u.department " +
                        "FROM users u WHERE u.username = ?")) {
                    userStmt.setString(1, adminUsername);

                    ResultSet userRs = userStmt.executeQuery();
                    if (userRs.next()) {
                        Long userId = userRs.getLong("id");

                        // Get user roles
                        List<String> roles = getUserRolesFromTenantSchema(userId, schemaName);

                        Map<String, Object> userInfo = Map.of(
                            "id", userId,
                            "username", userRs.getString("username"),
                            "email", userRs.getString("email"),
                            "firstName", userRs.getString("first_name"),
                            "lastName", userRs.getString("last_name"),
                            "department", userRs.getString("department"),
                            "roles", roles,
                            "tenantId", tenantId
                        );

                        return ResponseEntity.ok(userInfo);
                    } else {
                        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                            .body(Map.of("error", "Admin user not found for tenant: " + tenantId));
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error getting admin user for tenant: {}", tenantId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to get admin user information"));
        }
    }

    // Result classes
    public static class AuthenticationResult {
        private final boolean success;
        private final String message;
        private final String token;
        private final User user;
        private final List<String> roles;
        private final Tenant tenant;

        private AuthenticationResult(boolean success, String message, String token, User user, List<String> roles, Tenant tenant) {
            this.success = success;
            this.message = message;
            this.token = token;
            this.user = user;
            this.roles = roles;
            this.tenant = tenant;
        }

        public static AuthenticationResult success(String token, User user, List<String> roles, Tenant tenant) {
            return new AuthenticationResult(true, "Authentication successful", token, user, roles, tenant);
        }

        public static AuthenticationResult failure(String message) {
            return new AuthenticationResult(false, message, null, null, null, null);
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public String getToken() { return token; }
        public User getUser() { return user; }
        public List<String> getRoles() { return roles; }
        public Tenant getTenant() { return tenant; }
    }

    public static class RegistrationResult {
        private final boolean success;
        private final String message;
        private final User user;

        private RegistrationResult(boolean success, String message, User user) {
            this.success = success;
            this.message = message;
            this.user = user;
        }

        public static RegistrationResult success(User user) {
            return new RegistrationResult(true, "Registration successful", user);
        }

        public static RegistrationResult failure(String message) {
            return new RegistrationResult(false, message, null);
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public User getUser() { return user; }
    }
}
