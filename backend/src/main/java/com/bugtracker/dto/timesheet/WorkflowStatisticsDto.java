package com.bugtracker.dto.timesheet;

import com.bugtracker.model.TimesheetApprovalWorkflow;
import java.math.BigDecimal;

/**
 * DTO for workflow statistics data
 */
public class WorkflowStatisticsDto {
    private TimesheetApprovalWorkflow.WorkflowStatus status;
    private Long count;
    private BigDecimal averageHours;
    private BigDecimal totalHours;

    public WorkflowStatisticsDto() {}

    public WorkflowStatisticsDto(TimesheetApprovalWorkflow.WorkflowStatus status, Long count,
                                BigDecimal averageHours, BigDecimal totalHours) {
        this.status = status;
        this.count = count;
        this.averageHours = averageHours;
        this.totalHours = totalHours;
    }

    // Constructor for handling Double values from aggregate functions
    public WorkflowStatisticsDto(TimesheetApprovalWorkflow.WorkflowStatus status, Long count,
                                Double averageHours, Double totalHours) {
        this.status = status;
        this.count = count;
        this.averageHours = averageHours != null ? BigDecimal.valueOf(averageHours) : BigDecimal.ZERO;
        this.totalHours = totalHours != null ? BigDecimal.valueOf(totalHours) : BigDecimal.ZERO;
    }

    // Getters and setters
    public TimesheetApprovalWorkflow.WorkflowStatus getStatus() {
        return status;
    }

    public void setStatus(TimesheetApprovalWorkflow.WorkflowStatus status) {
        this.status = status;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public BigDecimal getAverageHours() {
        return averageHours;
    }

    public void setAverageHours(BigDecimal averageHours) {
        this.averageHours = averageHours;
    }

    public BigDecimal getTotalHours() {
        return totalHours;
    }

    public void setTotalHours(BigDecimal totalHours) {
        this.totalHours = totalHours;
    }
}
