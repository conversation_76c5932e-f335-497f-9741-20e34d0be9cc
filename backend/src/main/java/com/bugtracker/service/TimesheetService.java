package com.bugtracker.service;

import com.bugtracker.model.TimesheetEntry;
import com.bugtracker.model.TimesheetApprovalWorkflow;
import com.bugtracker.model.User;
import com.bugtracker.model.Issue;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Service interface for Timesheet Management
 */
public interface TimesheetService {

    // ===== Timesheet Entry Management =====

    /**
     * Create a new timesheet entry
     */
    TimesheetEntry createTimesheetEntry(Long userId, Long issueId, LocalDate entryDate, 
                                       BigDecimal hoursSpent, String description, 
                                       String activityType, Boolean billable);

    /**
     * Update an existing timesheet entry
     */
    TimesheetEntry updateTimesheetEntry(Long entryId, BigDecimal hoursSpent, 
                                       String description, String activityType, Boolean billable);

    /**
     * Delete a timesheet entry
     */
    void deleteTimesheetEntry(Long entryId);

    /**
     * Get timesheet entry by ID
     */
    Optional<TimesheetEntry> getTimesheetEntryById(Long entryId);

    /**
     * Get timesheet entries for user
     */
    Page<TimesheetEntry> getTimesheetEntriesByUser(Long userId, Pageable pageable);

    /**
     * Get timesheet entries for user and date range
     */
    Page<TimesheetEntry> getTimesheetEntriesByUserAndDateRange(Long userId, LocalDate startDate, 
                                                              LocalDate endDate, Pageable pageable);

    /**
     * Get today's timesheet entries for user
     */
    List<TimesheetEntry> getTodaysTimesheetEntries(Long userId);

    /**
     * Get timesheet entries for issue
     */
    Page<TimesheetEntry> getTimesheetEntriesByIssue(Long issueId, Pageable pageable);

    /**
     * Get all timesheet entries (admin only)
     */
    Page<TimesheetEntry> getAllTimesheetEntries(Pageable pageable);

    /**
     * Get timesheet entries by date range (admin only)
     */
    Page<TimesheetEntry> getTimesheetEntriesByDateRange(LocalDate startDate, LocalDate endDate, Pageable pageable);

    /**
     * Get pending approval entries
     */
    Page<TimesheetEntry> getPendingApprovalEntries(Pageable pageable);

    /**
     * Get pending approval entries for user
     */
    Page<TimesheetEntry> getPendingApprovalEntriesByUser(Long userId, Pageable pageable);

    // ===== Timesheet Approval Management =====

    /**
     * Approve timesheet entry
     */
    TimesheetEntry approveTimesheetEntry(Long entryId, Long approverId, String notes);

    /**
     * Reject timesheet entry
     */
    TimesheetEntry rejectTimesheetEntry(Long entryId, String notes);

    /**
     * Bulk approve timesheet entries
     */
    List<TimesheetEntry> bulkApproveTimesheetEntries(List<Long> entryIds, Long approverId, String notes);

    /**
     * Bulk reject timesheet entries
     */
    List<TimesheetEntry> bulkRejectTimesheetEntries(List<Long> entryIds, String notes);

    // ===== Workflow Management =====

    /**
     * Get or create workflow for user and week
     */
    TimesheetApprovalWorkflow getOrCreateWorkflow(Long userId, LocalDate weekDate);

    /**
     * Submit timesheet for approval
     */
    TimesheetApprovalWorkflow submitTimesheetForApproval(Long userId, LocalDate weekStartDate);

    /**
     * Approve timesheet workflow
     */
    TimesheetApprovalWorkflow approveTimesheetWorkflow(Long workflowId, Long reviewerId, String notes);

    /**
     * Reject timesheet workflow
     */
    TimesheetApprovalWorkflow rejectTimesheetWorkflow(Long workflowId, Long reviewerId, String notes);

    /**
     * Get workflows by user
     */
    Page<TimesheetApprovalWorkflow> getWorkflowsByUser(Long userId, Pageable pageable);

    /**
     * Get workflows pending approval
     */
    Page<TimesheetApprovalWorkflow> getWorkflowsPendingApproval(Pageable pageable);

    /**
     * Get current week workflow for user
     */
    Optional<TimesheetApprovalWorkflow> getCurrentWeekWorkflow(Long userId);

    // ===== Analytics and Reporting =====

    /**
     * Calculate total hours by user
     */
    BigDecimal getTotalHoursByUser(Long userId);

    /**
     * Calculate total hours by user and date range
     */
    BigDecimal getTotalHoursByUserAndDateRange(Long userId, LocalDate startDate, LocalDate endDate);

    /**
     * Calculate total hours by issue
     */
    BigDecimal getTotalHoursByIssue(Long issueId);

    /**
     * Calculate total billable hours by user
     */
    BigDecimal getTotalBillableHoursByUser(Long userId);

    /**
     * Get daily summary for user
     */
    Map<LocalDate, Map<String, Object>> getDailySummaryByUser(Long userId, LocalDate startDate, LocalDate endDate);

    /**
     * Get weekly summary for user
     */
    Map<LocalDate, Map<String, Object>> getWeeklySummaryByUser(Long userId, LocalDate startDate, LocalDate endDate);

    /**
     * Get activity type summary for user
     */
    Map<String, Map<String, Object>> getActivityTypeSummaryByUser(Long userId, LocalDate startDate, LocalDate endDate);

    /**
     * Get issue summary for user
     */
    Map<Long, Map<String, Object>> getIssueSummaryByUser(Long userId, LocalDate startDate, LocalDate endDate);

    /**
     * Get top users by total hours
     */
    List<Map<String, Object>> getTopUsersByTotalHours(LocalDate startDate, LocalDate endDate, int limit);

    /**
     * Get most worked issues
     */
    List<Map<String, Object>> getMostWorkedIssues(LocalDate startDate, LocalDate endDate, int limit);

    /**
     * Get approval statistics
     */
    Map<String, Object> getApprovalStatistics(LocalDate startDate, LocalDate endDate);

    /**
     * Get workflow statistics
     */
    Map<String, Object> getWorkflowStatistics(LocalDate startDate, LocalDate endDate);

    /**
     * Get user dashboard data
     */
    Map<String, Object> getUserDashboardData(Long userId, LocalDate startDate, LocalDate endDate);

    /**
     * Get admin dashboard data
     */
    Map<String, Object> getAdminDashboardData(LocalDate startDate, LocalDate endDate);

    // ===== Validation and Permissions =====

    /**
     * Check if user can edit timesheet entry
     */
    boolean canEditTimesheetEntry(Long userId, Long entryId);

    /**
     * Check if user can approve timesheet entries
     */
    boolean canApproveTimesheetEntries(Long userId);

    /**
     * Check if user can view all timesheets
     */
    boolean canViewAllTimesheets(Long userId);

    /**
     * Check if user can export timesheet data
     */
    boolean canExportTimesheetData(Long userId);

    /**
     * Validate timesheet entry data
     */
    void validateTimesheetEntry(Long userId, Long issueId, LocalDate entryDate, 
                               BigDecimal hoursSpent, String description, String activityType);

    // ===== Data Export =====

    /**
     * Export timesheet data to CSV
     */
    byte[] exportTimesheetDataToCsv(Long userId, LocalDate startDate, LocalDate endDate, 
                                   List<Long> userIds, List<Long> issueIds);

    /**
     * Export timesheet data to Excel
     */
    byte[] exportTimesheetDataToExcel(Long userId, LocalDate startDate, LocalDate endDate, 
                                     List<Long> userIds, List<Long> issueIds);

    /**
     * Get export history for user
     */
    List<Map<String, Object>> getExportHistory(Long userId, Pageable pageable);

    // ===== Utility Methods =====

    /**
     * Update workflow total hours
     */
    void updateWorkflowTotalHours(Long workflowId);

    /**
     * Recalculate all workflow totals for user
     */
    void recalculateWorkflowTotals(Long userId);

    /**
     * Clean up old timesheet data
     */
    void cleanupOldTimesheetData(LocalDate cutoffDate);

    /**
     * Get available activity types
     */
    List<String> getAvailableActivityTypes();

    /**
     * Get timesheet entry validation rules
     */
    Map<String, Object> getValidationRules();

    /**
     * Check for duplicate timesheet entries
     */
    boolean hasDuplicateEntry(Long userId, Long issueId, LocalDate entryDate, String activityType);

    /**
     * Get timesheet entry conflicts
     */
    List<TimesheetEntry> getConflictingEntries(Long userId, Long issueId, LocalDate entryDate, String activityType);
}
