import { useEffect, Component, ReactNode } from 'react';
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { Al<PERSON>, Container, Box, Button } from '@mui/material';
import Dashboard from './pages/Dashboard';
import Layout from './components/Layout';
import IssueList from './pages/IssueList';
import IssueBoard from './pages/IssueBoard';
import IssueCalendar from './pages/IssueCalendar';
import IssueDetail from './pages/IssueDetail';
import IssueImport from './pages/IssueImport';
import CreateIssuePage from './pages/CreateIssuePage';
import EditIssuePage from './pages/EditIssuePage';
import VoiceAssistantPage from './pages/VoiceAssistantPage';
import UserProfile from './pages/UserProfile';
import UserManagement from './pages/UserManagement';
import CreateUserPage from './pages/CreateUserPage';
import EditUserPage from './pages/EditUserPage';
import Reports from './pages/Reports';
import TimesheetEntryPage from './pages/TimesheetEntryPage';
import NotFound from './pages/NotFound';
import TestAuth from './pages/TestAuth';
import TestFixes from './pages/TestFixes';
import TestLookups from './pages/TestLookups';
import TenantSelection from './pages/TenantSelection';
import TenantLogin from './pages/TenantLogin';
import TenantRegistration from './pages/TenantRegistration';
import LandingPage from './pages/LandingPage';
import TenantRouteGuard from './components/TenantRouteGuard';
import RootRedirect from './components/RootRedirect';
import { useAppDispatch } from './store/hooks';
import tenantService from './services/tenantService';
import unifiedAuthManager from './services/unifiedAuthManager';
import { ROUTES } from './config/routes';

/**
 * Error Boundary for catching routing and component errors
 */
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class RoutingErrorBoundary extends Component<{ children: ReactNode }, ErrorBoundaryState> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('App routing error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <Container maxWidth="md">
          <Box sx={{ mt: 4, textAlign: 'center' }}>
            <Alert severity="error" sx={{ mb: 2 }}>
              <strong>Application Error:</strong> {this.state.error?.message || 'An unexpected error occurred'}
            </Alert>
            <Button variant="contained" onClick={() => this.setState({ hasError: false })}>
              Try Again
            </Button>
            <Button variant="outlined" onClick={() => window.location.href = '/welcome'} sx={{ ml: 2 }}>
              Go to Welcome Page
            </Button>
          </Box>
        </Container>
      );
    }

    return this.props.children;
  }
}

function App() {
  const dispatch = useAppDispatch();
  const location = useLocation();

  // Initialize authentication on app start (only for protected routes)
  useEffect(() => {
    const initializeApp = async () => {
      try {
        const isProtectedRoute = location.pathname.startsWith('/app');
        const isPublicRoute = [
          '/',
          '/welcome',
          '/tenant-selection',
          '/login',
          '/register-tenant'
        ].includes(location.pathname);

        // Only initialize auth for protected routes
        if (isProtectedRoute) {
          console.log('App: Initializing authentication for protected route');
          const isAuthenticated = await unifiedAuthManager.initializeAuth();

          if (isAuthenticated) {
            const tenantId = await tenantService.initializeTenantContext();
            console.log('App: Tenant context initialized:', tenantId);
          }
        } else if (isPublicRoute) {
          console.log('App: Public route accessed, skipping auth initialization');
          // For public routes, ensure no stale auth data interferes
          const hasStaleAuth = localStorage.getItem('auth_token') &&
                              !unifiedAuthManager.isAuthenticated();
          if (hasStaleAuth) {
            console.log('App: Clearing stale authentication data');
            unifiedAuthManager.clearAllAuthData();
            tenantService.clearTenantContext();
          }
        }

      } catch (error) {
        console.error('App: Failed to initialize app:', error);
      }
    };

    initializeApp();
  }, [dispatch, location.pathname]);

  return (
    <RoutingErrorBoundary>
      <Routes>
        {/* Public routes - no authentication required */}
        <Route path={ROUTES.PUBLIC.WELCOME} element={<LandingPage />} />
        <Route path={ROUTES.PUBLIC.TENANT_SELECTION} element={<TenantSelection />} />
        <Route path={ROUTES.PUBLIC.LOGIN} element={<TenantLogin />} />
        <Route path={ROUTES.PUBLIC.REGISTER_TENANT} element={<TenantRegistration />} />
        <Route path={ROUTES.PUBLIC.TEST_AUTH} element={<TestAuth />} />
        <Route path={ROUTES.PUBLIC.TEST_LOOKUPS_PUBLIC} element={<TestLookups />} />

        {/* Root route - intelligent redirect based on authentication state */}
        <Route path="/" element={<RootRedirect />} />

      {/* Protected routes - require tenant authentication */}
      <Route path="/app" element={
        <TenantRouteGuard requireAuth={true} requireTenant={true}>
          <Layout />
        </TenantRouteGuard>
      }>
        <Route index element={<Dashboard />} />
        <Route path="dashboard" element={<Dashboard />} />
        <Route path="issues">
          <Route index element={<IssueList />} />
          <Route path="create" element={<CreateIssuePage />} />
          <Route path="board" element={<IssueBoard />} />
          <Route path="calendar" element={<IssueCalendar />} />
          <Route path="import" element={<IssueImport />} />
          <Route path=":id" element={<IssueDetail />} />
          <Route path=":id/edit" element={<EditIssuePage />} />
        </Route>
        <Route path="voice-assistant" element={<VoiceAssistantPage />} />
        <Route path="profile" element={<UserProfile />} />
        <Route path="users">
          <Route index element={
            <TenantRouteGuard requireRoles={['ROLE_ADMIN']}>
              <UserManagement />
            </TenantRouteGuard>
          } />
          <Route path="create" element={
            <TenantRouteGuard requireRoles={['ROLE_ADMIN']}>
              <CreateUserPage />
            </TenantRouteGuard>
          } />
          <Route path=":id/edit" element={
            <TenantRouteGuard requireRoles={['ROLE_ADMIN']}>
              <EditUserPage />
            </TenantRouteGuard>
          } />
        </Route>
        <Route path="timesheet">
          <Route path="entry" element={<TimesheetEntryPage />} />
          <Route path="dashboard" element={<TimesheetEntryPage />} />
          <Route path="admin" element={
            <TenantRouteGuard requireRoles={['ROLE_ADMIN']}>
              <TimesheetEntryPage />
            </TenantRouteGuard>
          } />
          <Route path="approval" element={
            <TenantRouteGuard requireRoles={['ROLE_ADMIN']}>
              <TimesheetEntryPage />
            </TenantRouteGuard>
          } />
        </Route>
        <Route path="reports" element={<Reports />} />
        <Route path="test-fixes" element={<TestFixes />} />
        <Route path="test-lookups" element={<TestLookups />} />
      </Route>

        {/* Fallback route */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </RoutingErrorBoundary>
  );
}

export default App;
