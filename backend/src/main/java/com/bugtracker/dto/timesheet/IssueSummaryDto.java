package com.bugtracker.dto.timesheet;

import com.bugtracker.model.Issue;
import java.math.BigDecimal;

/**
 * DTO for issue summary data
 */
public class IssueSummaryDto {
    private Issue issue;
    private Long entryCount;
    private BigDecimal totalHours;

    public IssueSummaryDto() {}

    public IssueSummaryDto(Issue issue, Long entryCount, BigDecimal totalHours) {
        this.issue = issue;
        this.entryCount = entryCount;
        this.totalHours = totalHours;
    }

    // Constructor for Hibernate queries: SUM returns BigDecimal when field is BigDecimal
    // This constructor is not needed since SUM(BigDecimal) returns BigDecimal

    // Getters and setters
    public Issue getIssue() {
        return issue;
    }

    public void setIssue(Issue issue) {
        this.issue = issue;
    }

    public Long getEntryCount() {
        return entryCount;
    }

    public void setEntryCount(Long entryCount) {
        this.entryCount = entryCount;
    }

    public BigDecimal getTotalHours() {
        return totalHours;
    }

    public void setTotalHours(BigDecimal totalHours) {
        this.totalHours = totalHours;
    }
}
