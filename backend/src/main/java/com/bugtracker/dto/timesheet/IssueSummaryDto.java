package com.bugtracker.dto.timesheet;

import com.bugtracker.model.Issue;
import java.math.BigDecimal;

/**
 * DTO for issue summary data
 */
public class IssueSummaryDto {
    private Issue issue;
    private Long entryCount;
    private BigDecimal totalHours;

    public IssueSummaryDto() {}

    public IssueSummaryDto(Issue issue, Long entryCount, BigDecimal totalHours) {
        this.issue = issue;
        this.entryCount = entryCount;
        this.totalHours = totalHours;
    }

    // Constructor for handling Double values from aggregate functions
    public IssueSummaryDto(Issue issue, Long entryCount, Double totalHours) {
        this.issue = issue;
        this.entryCount = entryCount;
        this.totalHours = totalHours != null ? BigDecimal.valueOf(totalHours) : BigDecimal.ZERO;
    }

    // Getters and setters
    public Issue getIssue() {
        return issue;
    }

    public void setIssue(Issue issue) {
        this.issue = issue;
    }

    public Long getEntryCount() {
        return entryCount;
    }

    public void setEntryCount(Long entryCount) {
        this.entryCount = entryCount;
    }

    public BigDecimal getTotalHours() {
        return totalHours;
    }

    public void setTotalHours(BigDecimal totalHours) {
        this.totalHours = totalHours;
    }
}
