package com.bugtracker.dto.timesheet;

/**
 * DTO for approval statistics data
 */
public class ApprovalStatisticsDto {
    private Long totalEntries;
    private Long approvedEntries;
    private Long pendingEntries;
    private Long overdueEntries;

    public ApprovalStatisticsDto() {}

    public ApprovalStatisticsDto(Long totalEntries, Long approvedEntries, Long pendingEntries, Long overdueEntries) {
        this.totalEntries = totalEntries;
        this.approvedEntries = approvedEntries;
        this.pendingEntries = pendingEntries;
        this.overdueEntries = overdueEntries;
    }

    // Getters and setters
    public Long getTotalEntries() {
        return totalEntries;
    }

    public void setTotalEntries(Long totalEntries) {
        this.totalEntries = totalEntries;
    }

    public Long getApprovedEntries() {
        return approvedEntries;
    }

    public void setApprovedEntries(Long approvedEntries) {
        this.approvedEntries = approvedEntries;
    }

    public Long getPendingEntries() {
        return pendingEntries;
    }

    public void setPendingEntries(Long pendingEntries) {
        this.pendingEntries = pendingEntries;
    }

    public Long getOverdueEntries() {
        return overdueEntries;
    }

    public void setOverdueEntries(Long overdueEntries) {
        this.overdueEntries = overdueEntries;
    }
}
