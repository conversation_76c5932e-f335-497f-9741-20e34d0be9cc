import React, { useEffect, useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Divider,
  Grid
} from '@mui/material';
import {
  BugReport as BugReportIcon,
  Assignment as AssignmentIcon,
  Description as DescriptionIcon,
  CheckCircle as CheckCircleIcon,
  Psychology as PsychologyIcon,
  Engineering as EngineeringIcon
} from '@mui/icons-material';
import issueTypeFieldConfigService, { FieldDisplayInfo } from '../services/issueTypeFieldConfigService';

interface IssueTypeSpecificFieldsProps {
  issue: any;
  issueType: string;
}

interface ConditionalFields {
  required: string[];
  optional: string[];
  displayInfo: Record<string, FieldDisplayInfo>;
}

const IssueTypeSpecificFields: React.FC<IssueTypeSpecificFieldsProps> = ({ issue, issueType }) => {
  const [conditionalFields, setConditionalFields] = useState<ConditionalFields>({
    required: [],
    optional: [],
    displayInfo: {}
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchConditionalFields = async () => {
      if (issueType) {
        try {
          setLoading(true);
          const [requiredFields, optionalFields, displayInfo] = await Promise.all([
            issueTypeFieldConfigService.getRequiredFields(issueType),
            issueTypeFieldConfigService.getOptionalFields(issueType),
            issueTypeFieldConfigService.getFieldDisplayInfo(issueType)
          ]);

          setConditionalFields({
            required: requiredFields,
            optional: optionalFields,
            displayInfo
          });
        } catch (error) {
          console.error('Error fetching conditional fields:', error);
          // Use fallback empty state
          setConditionalFields({
            required: [],
            optional: [],
            displayInfo: {}
          });
        } finally {
          setLoading(false);
        }
      }
    };

    fetchConditionalFields();
  }, [issueType]);

  // Get the appropriate icon for each field type
  const getFieldIcon = (fieldName: string) => {
    switch (fieldName) {
      case 'stepsToReproduce':
        return <AssignmentIcon sx={{ mr: 1, color: 'text.secondary' }} />;
      case 'expectedResult':
        return <CheckCircleIcon sx={{ mr: 1, color: 'success.main' }} />;
      case 'actualResult':
        return <BugReportIcon sx={{ mr: 1, color: 'error.main' }} />;
      case 'businessJustification':
        return <PsychologyIcon sx={{ mr: 1, color: 'text.secondary' }} />;
      case 'acceptanceCriteria':
        return <CheckCircleIcon sx={{ mr: 1, color: 'text.secondary' }} />;
      case 'userStory':
        return <DescriptionIcon sx={{ mr: 1, color: 'text.secondary' }} />;
      case 'definitionOfDone':
        return <CheckCircleIcon sx={{ mr: 1, color: 'text.secondary' }} />;
      case 'researchNotes':
        return <PsychologyIcon sx={{ mr: 1, color: 'text.secondary' }} />;
      case 'technicalApproach':
        return <EngineeringIcon sx={{ mr: 1, color: 'text.secondary' }} />;
      default:
        return <DescriptionIcon sx={{ mr: 1, color: 'text.secondary' }} />;
    }
  };

  // Get field value from issue object
  const getFieldValue = (fieldName: string): string => {
    return issue[fieldName] || '';
  };

  // Check if there are any fields to display
  const hasFieldsToDisplay = Object.keys(conditionalFields.displayInfo).some(fieldName => 
    getFieldValue(fieldName).trim() !== ''
  );

  if (loading) {
    return null; // Don't show anything while loading
  }

  if (!hasFieldsToDisplay) {
    return null; // Don't render if no fields have values
  }

  return (
    <Box sx={{ mb: 3 }}>
      <Divider sx={{ my: 2 }}>
        <Typography variant="h6" color="text.secondary" sx={{ px: 2 }}>
          {issueType} Specific Fields
        </Typography>
      </Divider>

      <Grid container spacing={2}>
        {Object.keys(conditionalFields.displayInfo).map((fieldName) => {
          const displayInfo = conditionalFields.displayInfo[fieldName];
          const fieldValue = getFieldValue(fieldName);
          const isRequired = conditionalFields.required.includes(fieldName);

          // Only render fields that have values
          if (!fieldValue.trim()) {
            return null;
          }

          return (
            <Grid item xs={12} key={fieldName}>
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  {getFieldIcon(fieldName)}
                  <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                    {displayInfo.label}
                    {isRequired && (
                      <Typography component="span" color="error.main" sx={{ ml: 0.5 }}>
                        *
                      </Typography>
                    )}
                  </Typography>
                </Box>
                <Paper
                  variant="outlined"
                  sx={{
                    p: 2,
                    borderRadius: 2,
                    bgcolor: 'rgba(0,0,0,0.02)',
                    minHeight: '60px',
                    '&:hover': {
                      bgcolor: 'rgba(0,0,0,0.03)'
                    }
                  }}
                >
                  <Typography variant="body1" sx={{ whiteSpace: 'pre-line' }}>
                    {fieldValue}
                  </Typography>
                </Paper>
              </Box>
            </Grid>
          );
        })}
      </Grid>
    </Box>
  );
};

export default IssueTypeSpecificFields;
