package com.bugtracker.controller;

import com.bugtracker.model.Comment;
import com.bugtracker.model.Issue;
import com.bugtracker.model.User;
import com.bugtracker.payload.response.MessageResponse;
import com.bugtracker.security.services.UserDetailsImpl;
import com.bugtracker.service.CommentService;
import com.bugtracker.service.IssueService;
import com.bugtracker.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/comments")
public class CommentController {

    @Autowired
    private CommentService commentService;

    @Autowired
    private IssueService issueService;

    @Autowired
    private UserService userService;

    @GetMapping("/{id}")
    public ResponseEntity<?> getCommentById(@PathVariable Long id) {
        return commentService.getCommentById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/issue/{issueId}")
    public ResponseEntity<Page<Comment>> getCommentsByIssue(@PathVariable Long issueId, Pageable pageable) {
        return issueService.getIssueById(issueId)
                .map(issue -> ResponseEntity.ok(commentService.getCommentsByIssue(issue, pageable)))
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping("/issue/{issueId}")
    @Transactional
    public ResponseEntity<?> createComment(@PathVariable Long issueId, @RequestBody Comment comment) {
        try {
            // Security disabled - use default admin user
            Long defaultUserId = 1L; // Default to admin user

            // Create a minimal Issue proxy to avoid lazy loading issues
            Issue issueProxy = new Issue();
            issueProxy.setId(issueId);

            User user = userService.getUserById(defaultUserId)
                    .orElseThrow(() -> new RuntimeException("User not found"));

            comment.setIssue(issueProxy);
            comment.setUser(user);

            Comment createdComment = commentService.createComment(comment);

            // Return a very simple response to avoid any lazy loading issues
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Comment created successfully",
                "commentId", createdComment.getId()
            ));
        } catch (Exception e) {
            System.err.println("Error creating comment: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).body(Map.of(
                "success", false,
                "message", "Failed to create comment: " + e.getMessage()
            ));
        }
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or @commentSecurity.isCommentAuthor(#id)")
    public ResponseEntity<?> updateComment(@PathVariable Long id, @RequestBody Comment commentDetails) {
        return commentService.getCommentById(id)
                .map(comment -> {
                    comment.setContent(commentDetails.getContent());

                    Comment updatedComment = commentService.updateComment(comment);
                    return ResponseEntity.ok(updatedComment);
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or @commentSecurity.isCommentAuthor(#id)")
    public ResponseEntity<?> deleteComment(@PathVariable Long id) {
        return commentService.getCommentById(id)
                .map(comment -> {
                    commentService.deleteComment(id);
                    return ResponseEntity.ok(new MessageResponse("Comment deleted successfully"));
                })
                .orElse(ResponseEntity.notFound().build());
    }
}
