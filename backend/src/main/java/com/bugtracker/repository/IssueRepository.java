package com.bugtracker.repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.bugtracker.model.Issue;
import com.bugtracker.model.User;

@Repository
public interface IssueRepository extends JpaRepository<Issue, Long> {

    Optional<Issue> findByIdentifier(String identifier);

    Page<Issue> findByAssignee(User assignee, Pageable pageable);

    Page<Issue> findByReporter(User reporter, Pageable pageable);

    @Query("SELECT i FROM Issue i WHERE i.devCompletionDate < ?1 AND i.status != 'RESOLVED' AND i.status != 'CLOSED'")
    List<Issue> findOverdueIssues(LocalDateTime currentDate);

    @Query("SELECT i FROM Issue i WHERE i.assignee = ?1 AND i.status != 'RESOLVED' AND i.status != 'CLOSED'")
    List<Issue> findActiveIssuesByAssignee(User assignee);

    @Query("SELECT i FROM Issue i WHERE i.parent = ?1")
    List<Issue> findChildIssues(Issue parent);

    @Query("SELECT i FROM Issue i WHERE i.parent IS NULL")
    Page<Issue> findRootIssues(Pageable pageable);

    @Query("SELECT i FROM Issue i WHERE LOWER(i.title) LIKE LOWER(CONCAT('%', ?1, '%')) OR LOWER(i.description) LIKE LOWER(CONCAT('%', ?1, '%'))")
    Page<Issue> searchIssues(String keyword, Pageable pageable);

    /**
     * Find all issues with pagination (optimized for performance)
     */
    @Query("SELECT i FROM Issue i ORDER BY i.createdAt DESC")
    Page<Issue> findAllIssuesWithPagination(Pageable pageable);

    /**
     * Advanced search with multiple filters and pagination
     */
    @Query("SELECT i FROM Issue i WHERE " +
           "(:search IS NULL OR :search = '' OR " +
           " LOWER(i.title) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           " LOWER(i.description) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           " LOWER(i.identifier) LIKE LOWER(CONCAT('%', :search, '%'))) AND " +
           "(:status IS NULL OR :status = '' OR :status = 'ALL' OR i.status = :status) AND " +
           "(:type IS NULL OR :type = '' OR :type = 'ALL' OR i.type = :type) AND " +
           "(:priority IS NULL OR :priority = '' OR :priority = 'ALL' OR i.priority = :priority) AND " +
           "(:environment IS NULL OR :environment = '' OR :environment = 'ALL' OR i.environment = :environment) AND " +
           "(i.createdAt >= :startDate) AND " +
           "(i.createdAt < :endDate)")
    Page<Issue> findFilteredIssues(
            @Param("search") String search,
            @Param("status") String status,
            @Param("type") String type,
            @Param("priority") String priority,
            @Param("environment") String environment,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            Pageable pageable);

    /**
     * Find overdue issues with pagination
     */
    @Query("SELECT i FROM Issue i WHERE i.devCompletionDate < :currentDate AND i.status != 'RESOLVED' AND i.status != 'CLOSED'")
    Page<Issue> findOverdueIssuesWithPagination(@Param("currentDate") LocalDateTime currentDate, Pageable pageable);

    /**
     * Count total issues for dashboard stats
     */
    @Query("SELECT COUNT(i) FROM Issue i")
    long countAllIssues();

    /**
     * Count issues by status
     */
    @Query("SELECT COUNT(i) FROM Issue i WHERE i.status = :status")
    long countIssuesByStatus(@Param("status") String status);

    /**
     * Count issues by type
     */
    @Query("SELECT COUNT(i) FROM Issue i WHERE i.type = :type")
    long countIssuesByType(@Param("type") String type);

    /**
     * Count issues by priority
     */
    @Query("SELECT COUNT(i) FROM Issue i WHERE i.priority = :priority")
    long countIssuesByPriority(@Param("priority") String priority);
}
